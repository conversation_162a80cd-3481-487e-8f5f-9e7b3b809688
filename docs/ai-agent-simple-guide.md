# 按摩推拿连锁管理系统 - AI代理简易开发指南

**版本**: 2.0
**日期**: 2024-07-26
**适用范围**: 快速上手开发

---

## 🚀 快速开始

### 技术栈概览
```
后端框架: Spring Boot 2.7+ + Spring Cloud Alibaba
数据库: MySQL 8.0 + Redis + ClickHouse
消息队列: RocketMQ
分布式事务: Seata
容器化: Docker + Kubernetes
监控: Prometheus + Grafana
```

### 8个核心微服务
1. **用户与权限服务** (user-auth-service) - 认证授权、用户管理
2. **会员与营销服务** (member-marketing-service) - 会员体系、营销活动
3. **预约与排班服务** (appointment-schedule-service) - 预约管理、技师排班
4. **订单与支付服务** (order-payment-service) - 订单处理、支付集成
5. **库存与供应链服务** (inventory-supply-service) - 库存管理、采购管理
6. **数据与报表服务** (data-report-service) - 数据分析、报表生成
7. **AI与算法服务** (ai-algorithm-service) - 智能推荐、排班优化
8. **通知与消息服务** (notification-message-service) - 消息推送、通知管理

---

## 1. 核心原则 【必须遵守】

### 1.1 架构约束
- **微服务架构**：8个核心服务，单一职责
- **多租户隔离**：所有表必须包含 `tenant_id` 字段
- **六端差异化**：客户端、员工端、收银端、管理端、股东端、超管端
- **数据安全**：敏感数据加密存储，严格权限控制

### 1.2 多租户上下文
```java
// 租户上下文管理
@Component
public class TenantContext {
    private static final ThreadLocal<Long> TENANT_ID = new ThreadLocal<>();

    public static void setCurrentTenantId(Long tenantId) {
        TENANT_ID.set(tenantId);
    }

    public static Long getCurrentTenantId() {
        return TENANT_ID.get();
    }

    public static void clear() {
        TENANT_ID.remove();
    }
}

// 租户过滤器
@Component
public class TenantFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        String tenantId = extractTenantId((HttpServletRequest) request);
        TenantContext.setCurrentTenantId(Long.valueOf(tenantId));
        try {
            chain.doFilter(request, response);
        } finally {
            TenantContext.clear();
        }
    }
}
```

### 1.3 命名规范
```java
// 类名：PascalCase
public class AppointmentService {}

// 方法名：camelCase，动词开头
public void createAppointment() {}

// 变量名：camelCase
private String customerName;

// 常量：UPPER_SNAKE_CASE
public static final String DEFAULT_TIME_ZONE = "Asia/Shanghai";

// 数据库表：snake_case
appointments, customers, orders
```

---

## 2. 项目结构 【标准模板】

```
src/main/java/com/massage/
├── controller/     # REST控制器
│   ├── CustomerController.java
│   ├── AppointmentController.java
│   └── OrderController.java
├── service/        # 业务逻辑层
│   ├── CustomerService.java
│   ├── AppointmentService.java
│   └── OrderService.java
├── repository/     # 数据访问层
│   ├── CustomerRepository.java
│   └── AppointmentRepository.java
├── entity/         # 实体类
│   ├── Customer.java
│   ├── Appointment.java
│   └── Order.java
├── dto/           # 数据传输对象
│   ├── request/   # 请求DTO
│   └── response/  # 响应DTO
├── config/        # 配置类
│   ├── SecurityConfig.java
│   ├── DatabaseConfig.java
│   └── RedisConfig.java
├── common/        # 通用工具类
│   ├── exception/ # 异常处理
│   ├── utils/     # 工具类
│   └── constants/ # 常量定义
└── mapper/        # 对象映射
```

---

## 3. 核心业务实体

### 3.1 基础实体类
```java
// 所有实体的基类
@MappedSuperclass
@Data
public abstract class BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @Column(name = "created_time")
    private LocalDateTime createdTime;

    @Column(name = "updated_time")
    private LocalDateTime updatedTime;

    @Column(name = "deleted")
    private Boolean deleted = false;

    @PrePersist
    protected void onCreate() {
        createdTime = LocalDateTime.now();
        updatedTime = LocalDateTime.now();
        if (tenantId == null) {
            tenantId = TenantContext.getCurrentTenantId();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedTime = LocalDateTime.now();
    }
}
```

### 3.2 客户实体
```java
@Entity
@Table(name = "customers")
@Data
@EqualsAndHashCode(callSuper = true)
public class Customer extends BaseEntity {

    @Column(name = "name", nullable = false)
    private String name;

    @Convert(converter = EncryptedStringConverter.class)
    @Column(name = "phone")
    private String phone;

    @Enumerated(EnumType.STRING)
    private Gender gender;

    @Column(name = "birth_date")
    private LocalDate birthDate;

    @Convert(converter = EncryptedStringConverter.class)
    @Column(name = "health_record")
    private String healthRecord;

    @Enumerated(EnumType.STRING)
    private MemberLevel memberLevel;

    @Column(name = "total_consumption", precision = 10, scale = 2)
    private BigDecimal totalConsumption = BigDecimal.ZERO;
}
```

### 3.3 预约实体
```java
@Entity
@Table(name = "appointments")
@Data
@EqualsAndHashCode(callSuper = true)
public class Appointment extends BaseEntity {

    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @Column(name = "technician_id", nullable = false)
    private Long technicianId;

    @Column(name = "service_id", nullable = false)
    private Long serviceId;

    @Column(name = "appointment_time", nullable = false)
    private LocalDateTime appointmentTime;

    @Column(name = "duration_minutes", nullable = false)
    private Integer durationMinutes;

    @Enumerated(EnumType.STRING)
    private AppointmentStatus status;

    @Column(name = "room_id")
    private Long roomId;

    @Column(name = "notes")
    private String notes;
}
```

---

## 4. API设计规范

### 4.1 RESTful标准
```java
// 预约管理API
GET    /api/v1/appointments        // 查询预约列表
GET    /api/v1/appointments/{id}   // 查询预约详情
POST   /api/v1/appointments        // 创建预约
PUT    /api/v1/appointments/{id}   // 更新预约
DELETE /api/v1/appointments/{id}   // 取消预约

// 客户管理API
GET    /api/v1/customers           // 查询客户列表
POST   /api/v1/customers           // 创建客户
PUT    /api/v1/customers/{id}      // 更新客户信息
GET    /api/v1/customers/{id}/appointments  // 查询客户预约记录
```

### 4.2 统一响应格式
```java
// 成功响应
@Data
@Builder
public class ApiResponse<T> {
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;

    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
            .code(200)
            .message("success")
            .data(data)
            .timestamp(System.currentTimeMillis())
            .build();
    }

    public static <T> ApiResponse<T> error(Integer code, String message) {
        return ApiResponse.<T>builder()
            .code(code)
            .message(message)
            .timestamp(System.currentTimeMillis())
            .build();
    }
}

// 分页响应
@Data
@Builder
public class PageResult<T> {
    private Integer pageNum;
    private Integer pageSize;
    private Long total;
    private Integer pages;
    private List<T> list;
}
```

### 4.3 权限控制注解
```java
// 六端权限枚举
public enum ClientType {
    CUSTOMER("客户端", Arrays.asList("APPOINTMENT_CREATE", "ORDER_VIEW")),
    EMPLOYEE("员工端", Arrays.asList("APPOINTMENT_VIEW", "CUSTOMER_VIEW")),
    CASHIER("收银端", Arrays.asList("ORDER_CREATE", "PAYMENT_PROCESS")),
    MANAGER("管理端", Arrays.asList("REPORT_VIEW", "STAFF_MANAGE")),
    SHAREHOLDER("股东端", Arrays.asList("FINANCIAL_VIEW", "PROFIT_VIEW")),
    ADMIN("超管端", Arrays.asList("SYSTEM_CONFIG", "TENANT_MANAGE"));
}

// 权限验证
@PreAuthorize("hasPermission(#customerId, 'CUSTOMER', 'READ')")
public CustomerDTO getCustomer(Long customerId) {
    return customerService.findById(customerId);
}
```

---

## 5. 核心业务规则

### 5.1 预约管理
- **时间冲突检查**：同一技师同一时间只能有一个预约
- **资源预约**：特殊房间/设备需要独立预约
- **取消规则**：提前2小时可免费取消

### 5.2 会员体系
- **等级规则**：按消费金额自动升级
- **积分规则**：消费1元=1积分，100积分=1元
- **储值优惠**：充值金额越大优惠越多

### 5.3 员工提成
- **基础提成**：按项目实收金额的固定比例
- **阶梯提成**：月业绩达标后提成比例递增
- **团队奖励**：门店整体业绩奖励

### 5.4 库存管理
- **自动扣减**：订单确认后自动扣减库存
- **预警机制**：库存低于安全线自动预警
- **盘点流程**：定期盘点，差异调整

---

## 6. 数据安全要求

### 6.1 敏感数据加密
```java
// 加密转换器
@Converter
public class EncryptedStringConverter implements AttributeConverter<String, String> {

    @Override
    public String convertToDatabaseColumn(String attribute) {
        if (attribute == null) return null;
        return AESUtil.encrypt(attribute);
    }

    @Override
    public String convertToEntityAttribute(String dbData) {
        if (dbData == null) return null;
        return AESUtil.decrypt(dbData);
    }
}

// AES加密工具类
@Component
public class AESUtil {

    @Value("${app.security.aes.key}")
    private String secretKey;

    public static String encrypt(String plainText) {
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            byte[] encrypted = cipher.doFinal(plainText.getBytes());
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new SecurityException("数据加密失败", e);
        }
    }

    public static String decrypt(String encryptedText) {
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(encryptedText));
            return new String(decrypted);
        } catch (Exception e) {
            throw new SecurityException("数据解密失败", e);
        }
    }
}
```

### 6.2 权限控制
```java
// 权限验证服务
@Service
public class PermissionService {

    public boolean hasPermission(Long userId, String resource, String action) {
        // 1. 获取用户角色
        List<Role> roles = userRoleService.getUserRoles(userId);

        // 2. 检查权限
        return roles.stream()
            .flatMap(role -> role.getPermissions().stream())
            .anyMatch(permission ->
                permission.getResource().equals(resource) &&
                permission.getAction().equals(action));
    }

    public boolean hasDataPermission(Long userId, Long resourceId, String resourceType) {
        // 数据权限验证：只能访问本租户数据
        Long userTenantId = userService.getUserTenantId(userId);
        Long resourceTenantId = getResourceTenantId(resourceId, resourceType);

        return Objects.equals(userTenantId, resourceTenantId);
    }
}
```

### 6.3 操作审计
```java
// 审计日志实体
@Entity
@Table(name = "audit_logs")
public class AuditLog {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long userId;
    private String username;
    private String operation;
    private String resourceType;
    private Long resourceId;
    private String details;
    private String ipAddress;
    private LocalDateTime operationTime;
}

// 审计切面
@Aspect
@Component
public class AuditAspect {

    @Around("@annotation(Auditable)")
    public Object audit(ProceedingJoinPoint joinPoint) throws Throwable {
        // 记录操作前状态
        Object result = joinPoint.proceed();

        // 记录审计日志
        AuditLog auditLog = AuditLog.builder()
            .userId(getCurrentUserId())
            .operation(joinPoint.getSignature().getName())
            .operationTime(LocalDateTime.now())
            .build();

        auditLogService.save(auditLog);

        return result;
    }
}
```

---

## 7. 性能要求

### 7.1 响应时间
- **查询接口**：< 200ms
- **创建/更新**：< 500ms
- **报表查询**：< 2s

### 7.2 并发处理
- **预约冲突**：使用分布式锁
- **库存扣减**：使用乐观锁
- **支付处理**：幂等性保证

---

## 8. 代码模板

### 8.1 完整Controller模板
```java
@RestController
@RequestMapping("/api/v1/customers")
@Slf4j
@Validated
public class CustomerController {

    private final CustomerService customerService;

    @GetMapping
    @PreAuthorize("hasPermission('CUSTOMER', 'READ')")
    public ResponseEntity<ApiResponse<PageResult<CustomerDTO>>> getCustomers(
            @Valid CustomerQueryRequest request) {

        log.info("查询客户列表，参数: {}", request);
        PageResult<CustomerDTO> result = customerService.getCustomers(request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasPermission(#id, 'CUSTOMER', 'READ')")
    public ResponseEntity<ApiResponse<CustomerDTO>> getCustomer(@PathVariable Long id) {

        log.info("查询客户详情，ID: {}", id);
        CustomerDTO result = customerService.getCustomerById(id);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @PostMapping
    @PreAuthorize("hasPermission('CUSTOMER', 'CREATE')")
    @Auditable(operation = "CREATE_CUSTOMER")
    public ResponseEntity<ApiResponse<CustomerDTO>> createCustomer(
            @Valid @RequestBody CreateCustomerRequest request) {

        log.info("创建客户，参数: {}", request);
        CustomerDTO result = customerService.createCustomer(request);
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse.success(result));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasPermission(#id, 'CUSTOMER', 'UPDATE')")
    @Auditable(operation = "UPDATE_CUSTOMER")
    public ResponseEntity<ApiResponse<CustomerDTO>> updateCustomer(
            @PathVariable Long id,
            @Valid @RequestBody UpdateCustomerRequest request) {

        log.info("更新客户，ID: {}, 参数: {}", id, request);
        CustomerDTO result = customerService.updateCustomer(id, request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
}
```

### 8.2 完整Service模板
```java
@Service
@Transactional
@Slf4j
public class CustomerService {

    private final CustomerRepository customerRepository;
    private final CustomerMapper customerMapper;
    private final ApplicationEventPublisher eventPublisher;

    @Transactional(readOnly = true)
    public PageResult<CustomerDTO> getCustomers(CustomerQueryRequest request) {
        log.debug("查询客户列表，参数: {}", request);

        // 构建查询条件
        Specification<Customer> spec = buildSpecification(request);
        Pageable pageable = PageRequest.of(
            request.getPageNum() - 1,
            request.getPageSize(),
            Sort.by(Sort.Direction.DESC, "createdTime")
        );

        Page<Customer> page = customerRepository.findAll(spec, pageable);

        List<CustomerDTO> dtos = page.getContent().stream()
            .map(customerMapper::toDTO)
            .collect(Collectors.toList());

        return PageResult.<CustomerDTO>builder()
            .pageNum(request.getPageNum())
            .pageSize(request.getPageSize())
            .total(page.getTotalElements())
            .pages(page.getTotalPages())
            .list(dtos)
            .build();
    }

    @Transactional(readOnly = true)
    public CustomerDTO getCustomerById(Long id) {
        log.debug("查询客户详情，ID: {}", id);

        Customer customer = customerRepository.findById(id)
            .orElseThrow(() -> new NotFoundException("客户不存在，ID: " + id));

        return customerMapper.toDTO(customer);
    }

    public CustomerDTO createCustomer(CreateCustomerRequest request) {
        log.info("创建客户，参数: {}", request);

        // 1. 业务验证
        validateCreateRequest(request);

        // 2. 构建实体
        Customer customer = customerMapper.toEntity(request);
        customer.setTenantId(TenantContext.getCurrentTenantId());
        customer.setMemberLevel(MemberLevel.BRONZE);

        // 3. 保存实体
        customer = customerRepository.save(customer);

        // 4. 发布事件
        eventPublisher.publishEvent(new CustomerCreatedEvent(customer.getId()));

        log.info("客户创建成功，ID: {}", customer.getId());
        return customerMapper.toDTO(customer);
    }

    private Specification<Customer> buildSpecification(CustomerQueryRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 租户隔离
            predicates.add(criteriaBuilder.equal(root.get("tenantId"),
                TenantContext.getCurrentTenantId()));

            // 软删除过滤
            predicates.add(criteriaBuilder.equal(root.get("deleted"), false));

            // 其他查询条件
            if (StringUtils.hasText(request.getName())) {
                predicates.add(criteriaBuilder.like(root.get("name"),
                    "%" + request.getName() + "%"));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
```

---

## 9. 测试模板

### 9.1 单元测试模板
```java
@ExtendWith(MockitoExtension.class)
class CustomerServiceTest {

    @Mock
    private CustomerRepository customerRepository;

    @Mock
    private CustomerMapper customerMapper;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @InjectMocks
    private CustomerService customerService;

    @Test
    void createCustomer_Success() {
        // Given
        CreateCustomerRequest request = CreateCustomerRequest.builder()
            .name("张三")
            .phone("***********")
            .gender(Gender.MALE)
            .build();

        Customer entity = new Customer();
        entity.setId(1L);
        entity.setName("张三");

        CustomerDTO expectedDto = CustomerDTO.builder()
            .id(1L)
            .name("张三")
            .build();

        when(customerMapper.toEntity(request)).thenReturn(entity);
        when(customerRepository.save(any(Customer.class))).thenReturn(entity);
        when(customerMapper.toDTO(entity)).thenReturn(expectedDto);

        // When
        CustomerDTO result = customerService.createCustomer(request);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getName()).isEqualTo("张三");

        verify(customerRepository).save(any(Customer.class));
        verify(eventPublisher).publishEvent(any(CustomerCreatedEvent.class));
    }

    @Test
    void createCustomer_DuplicatePhone_ThrowsException() {
        // Given
        CreateCustomerRequest request = CreateCustomerRequest.builder()
            .name("张三")
            .phone("***********")
            .build();

        when(customerRepository.existsByPhoneAndDeletedFalse("***********"))
            .thenReturn(true);

        // When & Then
        assertThatThrownBy(() -> customerService.createCustomer(request))
            .isInstanceOf(BusinessException.class)
            .hasMessage("手机号已存在");
    }
}
```

### 9.2 集成测试模板
```java
@SpringBootTest
@Transactional
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
class CustomerControllerIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Autowired
    private CustomerRepository customerRepository;

    @Test
    void createCustomer_Success() {
        // Given
        CreateCustomerRequest request = CreateCustomerRequest.builder()
            .name("张三")
            .phone("***********")
            .gender(Gender.MALE)
            .build();

        // When
        ResponseEntity<ApiResponse> response = restTemplate.postForEntity(
            "/api/v1/customers",
            request,
            ApiResponse.class
        );

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody().getCode()).isEqualTo(200);

        // 验证数据库
        List<Customer> customers = customerRepository.findAll();
        assertThat(customers).hasSize(1);
        assertThat(customers.get(0).getName()).isEqualTo("张三");
    }
}
```

---

## 10. 部署配置

### 10.1 Docker配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  user-auth-service:
    image: massage/user-auth-service:latest
    ports:
      - "8081:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - SEATA_SERVER_ADDR=seata:8091
    depends_on:
      - mysql
      - redis
      - seata
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: massage_db
      MYSQL_USER: massage_user
      MYSQL_PASSWORD: massage_pass
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"

  seata:
    image: seataio/seata-server:latest
    ports:
      - "8091:8091"
    environment:
      - SEATA_CONFIG_NAME=file:/root/seata-config/registry
    volumes:
      - ./seata-config:/root/seata-config

volumes:
  mysql_data:
  redis_data:
```

### 10.2 应用配置
```yaml
# application-prod.yml
spring:
  datasource:
    url: jdbc:mysql://${MYSQL_HOST:localhost}:3306/massage_db?useSSL=false&serverTimezone=Asia/Shanghai
    username: ${MYSQL_USER:massage_user}
    password: ${MYSQL_PASSWORD:massage_pass}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000

  redis:
    host: ${REDIS_HOST:localhost}
    port: 6379
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 8
        min-idle: 2

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: massage_tx_group
  service:
    vgroup-mapping:
      massage_tx_group: default
  registry:
    type: nacos
    nacos:
      server-addr: ${NACOS_SERVER_ADDR:localhost:8848}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.massage: INFO
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
```

---

## 11. 开发检查清单

### 11.1 编码阶段 ✅
- [ ] 命名规范统一（类名PascalCase，方法名camelCase）
- [ ] 包含租户隔离字段 `tenant_id`
- [ ] 敏感数据加密存储（手机号、身份证、健康档案）
- [ ] 权限注解 `@PreAuthorize` 添加完整
- [ ] 异常处理完善（业务异常、系统异常）
- [ ] 日志记录完整（INFO级别记录关键操作）
- [ ] 参数验证充分（`@Valid`、`@NotNull`等）
- [ ] 审计注解 `@Auditable` 添加到敏感操作

### 11.2 测试阶段 ✅
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 集成测试覆盖主要业务流程
- [ ] 权限控制测试（六端权限验证）
- [ ] 多租户隔离测试
- [ ] 性能测试达标（API响应时间 < 500ms）
- [ ] 安全测试通过（SQL注入、XSS防护）

### 11.3 部署阶段 ✅
- [ ] Docker镜像构建成功
- [ ] 环境变量配置正确
- [ ] 健康检查端点配置
- [ ] 监控告警设置（Prometheus + Grafana）
- [ ] 日志收集配置（ELK Stack）
- [ ] 数据库迁移脚本准备

---

## 12. 常见问题解决

### 12.1 多租户数据泄露
**问题**：不同租户可以看到其他租户的数据
**原因**：查询条件中缺少租户过滤
**解决方案**：
```java
// 在Repository中添加租户过滤
@Query("SELECT c FROM Customer c WHERE c.tenantId = :#{T(com.massage.common.TenantContext).getCurrentTenantId()}")
List<Customer> findAllByCurrentTenant();

// 在Specification中添加租户条件
predicates.add(criteriaBuilder.equal(root.get("tenantId"), TenantContext.getCurrentTenantId()));
```

### 12.2 预约时间冲突
**问题**：同一技师同一时间被重复预约
**原因**：并发情况下缺少锁机制
**解决方案**：
```java
// 使用Redis分布式锁
@Service
public class AppointmentService {

    public AppointmentResult createAppointment(CreateAppointmentRequest request) {
        String lockKey = "appointment:" + request.getTechnicianId() + ":" + request.getTimeSlot();

        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                return doCreateAppointment(request);
            } else {
                throw new BusinessException("预约冲突，请重试");
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

### 12.3 分布式事务失败
**问题**：跨服务操作部分成功部分失败
**原因**：Seata配置不正确或网络问题
**解决方案**：
```java
// 确保事务注解正确
@GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 30000)
public OrderResult createOrder(CreateOrderRequest request) {
    // 业务逻辑
}

// 检查Seata配置
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: massage_tx_group
```

### 12.4 性能问题排查
**问题**：API响应时间过长
**排查步骤**：
1. 检查慢查询日志
2. 分析数据库索引使用情况
3. 查看缓存命中率
4. 检查JVM GC情况

**解决方案**：
```java
// 添加数据库索引
CREATE INDEX idx_tenant_appointment_time ON appointments(tenant_id, appointment_time);
CREATE INDEX idx_customer_phone ON customers(tenant_id, phone);

// 使用缓存
@Cacheable(value = "customers", key = "#id")
public CustomerDTO getCustomerById(Long id) {
    return customerRepository.findById(id).map(customerMapper::toDTO).orElse(null);
}
```

### 12.5 内存泄漏问题
**问题**：应用运行时间长后内存持续增长
**原因**：缓存无限增长、对象未释放
**解决方案**：
```java
// 设置缓存过期时间
@Cacheable(value = "customers", key = "#id", unless = "#result == null")
@CacheEvict(value = "customers", allEntries = true)
@Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点清理
public void clearCache() {
    // 清理缓存
}

// 正确关闭资源
try (FileInputStream fis = new FileInputStream(file)) {
    // 处理文件
} catch (IOException e) {
    log.error("文件处理失败", e);
}
```

---

## 13. 快速参考

### 13.1 核心注解
```java
@RestController          // REST控制器
@Service                // 业务服务
@Repository             // 数据访问
@Entity                 // JPA实体
@PreAuthorize           // 权限控制
@Transactional          // 事务管理
@GlobalTransactional    // 分布式事务
@Cacheable              // 缓存
@Auditable              // 审计日志
@Valid                  // 参数验证
```

### 13.2 常用工具类
```java
TenantContext.getCurrentTenantId()    // 获取当前租户ID
AESUtil.encrypt(data)                 // 数据加密
AESUtil.decrypt(data)                 // 数据解密
ApiResponse.success(data)             // 成功响应
ApiResponse.error(code, message)      // 错误响应
```

### 13.3 关键配置
```yaml
# 数据库连接池
spring.datasource.hikari.maximum-pool-size: 20

# Redis连接池
spring.redis.lettuce.pool.max-active: 20

# JPA配置
spring.jpa.hibernate.ddl-auto: validate

# Seata事务
seata.enabled: true
```

---

## 总结

本简易开发指南提供了按摩推拿连锁管理系统开发的核心要点：

### ✅ 核心特性
- **微服务架构**：8个核心服务，职责清晰
- **多租户隔离**：数据安全，租户独立
- **六端差异化**：权限精确控制
- **数据加密**：敏感信息安全存储
- **分布式事务**：跨服务数据一致性

### ✅ 开发规范
- **命名统一**：遵循Java命名规范
- **结构清晰**：标准的分层架构
- **异常处理**：完善的错误处理机制
- **日志记录**：详细的操作日志
- **测试覆盖**：充分的单元和集成测试

### ✅ 质量保证
- **性能优化**：缓存、索引、连接池
- **安全防护**：权限控制、数据加密、审计日志
- **监控告警**：健康检查、指标监控
- **容器化部署**：Docker、Kubernetes支持

遵循本指南开发，可以快速构建出高质量、高性能、高安全的按摩推拿连锁管理系统。

**📖 更多详细信息请参考完整开发指南文档**