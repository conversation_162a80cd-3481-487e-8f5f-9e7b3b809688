# 按摩推拿连锁管理系统 - AI代理编程规则与用户指南

**版本**: 2.0  
**日期**: 2024-07-26  
**适用范围**: 按摩推拿连锁管理系统全栈开发

---

## 目录

- [1. 总体原则和架构约束](#1-总体原则和架构约束)
- [2. 代码规范和开发标准](#2-代码规范和开发标准)
- [3. 业务逻辑实现指导](#3-业务逻辑实现指导)
- [4. 数据安全和隐私保护](#4-数据安全和隐私保护)
- [5. 测试和质量保证](#5-测试和质量保证)
- [6. 部署和运维规范](#6-部署和运维规范)
- [7. 特殊场景处理指南](#7-特殊场景处理指南)
- [8. AI代理使用指南](#8-ai代理使用指南)
- [9. 开发阶段检查清单](#9-开发阶段检查清单)
- [10. 故障排查指南](#10-故障排查指南)

---

## 1. 总体原则和架构约束

### 1.1 微服务架构原则 【必须】

#### 核心原则
- **单一职责**: 每个微服务只负责一个业务领域
- **服务自治**: 每个服务拥有独立的数据库和部署周期
- **接口标准化**: 统一使用RESTful API和JSON数据格式
- **故障隔离**: 服务间故障不相互影响

#### 服务边界划分 【必须】
```java
// 8个核心微服务，严格按照业务领域划分
public enum ServiceDomain {
    USER_AUTH("用户与权限服务", "user-auth-service"),
    MEMBER_MARKETING("会员与营销服务", "member-marketing-service"),
    APPOINTMENT_SCHEDULE("预约与排班服务", "appointment-schedule-service"),
    ORDER_PAYMENT("订单与支付服务", "order-payment-service"),
    INVENTORY_SUPPLY("库存与供应链服务", "inventory-supply-service"),
    DATA_REPORT("数据与报表服务", "data-report-service"),
    AI_ALGORITHM("AI与算法服务", "ai-algorithm-service"),
    NOTIFICATION_MESSAGE("通知与消息服务", "notification-message-service");
}
```

#### 多租户数据隔离 【必须】
```java
// 所有业务实体必须包含租户隔离字段
@Entity
@Table(name = "appointments")
@FilterDef(name = "tenantFilter", parameters = @ParamDef(name = "tenantId", type = "long"))
@Filter(name = "tenantFilter", condition = "tenant_id = :tenantId")
public class Appointment {
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;  // 租户ID（连锁品牌）
    
    @Column(name = "store_id", nullable = false) 
    private Long storeId;   // 门店ID
    
    // 自动注入租户上下文
    @PrePersist
    public void prePersist() {
        this.tenantId = TenantContext.getCurrentTenantId();
    }
}

// 租户上下文管理
@Component
public class TenantContext {
    private static final ThreadLocal<Long> currentTenant = new ThreadLocal<>();
    
    public static void setCurrentTenantId(Long tenantId) {
        currentTenant.set(tenantId);
    }
    
    public static Long getCurrentTenantId() {
        return currentTenant.get();
    }
    
    public static void clear() {
        currentTenant.remove();
    }
}
```

### 1.2 六端差异化架构 【必须】

#### 权限矩阵设计
```java
@RestController
@RequestMapping("/api/v1/appointments")
public class AppointmentController {

    @GetMapping
    @PreAuthorize("hasAnyRole('CUSTOMER', 'EMPLOYEE', 'CASHIER', 'MANAGER', 'SHAREHOLDER', 'ADMIN')")
    public ResponseEntity<PageResult<AppointmentDTO>> getAppointments(
            @RequestParam(required = false) String clientType,
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "20") int pageSize) {

        // 根据客户端类型返回不同数据视图
        AppointmentQuery query = AppointmentQuery.builder()
            .clientType(ClientType.valueOf(clientType))
            .pageNum(pageNum)
            .pageSize(pageSize)
            .build();

        return ResponseEntity.ok(appointmentService.getAppointments(query));
    }
}

// 客户端类型枚举
public enum ClientType {
    CUSTOMER("顾客端", Arrays.asList("VIEW_MY_APPOINTMENTS", "CANCEL_APPOINTMENT")),
    EMPLOYEE("员工端", Arrays.asList("VIEW_WORK_SCHEDULE", "UPDATE_SERVICE_STATUS")),
    CASHIER("收银端", Arrays.asList("CREATE_ORDER", "PROCESS_PAYMENT")),
    MANAGER("店长端", Arrays.asList("VIEW_STORE_DATA", "MANAGE_SCHEDULE")),
    SHAREHOLDER("股东端", Arrays.asList("VIEW_FINANCIAL_REPORT", "VIEW_DIVIDEND")),
    ADMIN("总部端", Arrays.asList("MANAGE_ALL_STORES", "SYSTEM_CONFIG"));

    private final String description;
    private final List<String> permissions;
}
```

#### 六端权限详细矩阵 【必须】
```java
// 详细权限控制实现
@Component
public class ClientPermissionManager {

    public boolean hasPermission(ClientType clientType, String resource, String action) {
        switch (clientType) {
            case CUSTOMER:
                return validateCustomerPermission(resource, action);
            case EMPLOYEE:
                return validateEmployeePermission(resource, action);
            case CASHIER:
                return validateCashierPermission(resource, action);
            case MANAGER:
                return validateManagerPermission(resource, action);
            case SHAREHOLDER:
                return validateShareholderPermission(resource, action);
            case ADMIN:
                return validateAdminPermission(resource, action);
            default:
                return false;
        }
    }

    private boolean validateCustomerPermission(String resource, String action) {
        // 顾客端权限：只能查看和操作自己的数据
        Map<String, List<String>> customerPermissions = Map.of(
            "appointments", Arrays.asList("VIEW_MY", "CREATE", "CANCEL"),
            "health_records", Arrays.asList("VIEW_MY", "UPDATE_MY"),
            "orders", Arrays.asList("VIEW_MY", "CREATE"),
            "coupons", Arrays.asList("VIEW_MY", "USE")
        );
        return customerPermissions.getOrDefault(resource, Collections.emptyList()).contains(action);
    }

    private boolean validateEmployeePermission(String resource, String action) {
        // 员工端权限：查看工作安排，更新服务状态
        Map<String, List<String>> employeePermissions = Map.of(
            "appointments", Arrays.asList("VIEW_ASSIGNED", "UPDATE_STATUS"),
            "schedules", Arrays.asList("VIEW_MY", "REQUEST_CHANGE"),
            "customers", Arrays.asList("VIEW_BASIC_INFO"),
            "services", Arrays.asList("VIEW", "UPDATE_PROGRESS")
        );
        return employeePermissions.getOrDefault(resource, Collections.emptyList()).contains(action);
    }

    private boolean validateManagerPermission(String resource, String action) {
        // 店长端权限：管理门店运营
        Map<String, List<String>> managerPermissions = Map.of(
            "appointments", Arrays.asList("VIEW_STORE", "CREATE", "UPDATE", "CANCEL"),
            "employees", Arrays.asList("VIEW_STORE", "MANAGE_SCHEDULE"),
            "reports", Arrays.asList("VIEW_STORE_FINANCIAL", "VIEW_STORE_OPERATIONAL"),
            "inventory", Arrays.asList("VIEW_STORE", "UPDATE_STORE"),
            "customers", Arrays.asList("VIEW_STORE", "MANAGE_STORE")
        );
        return managerPermissions.getOrDefault(resource, Collections.emptyList()).contains(action);
    }
}
```

#### 响应数据脱敏 【必须】
```java
@JsonView(Views.class)
public class CustomerDTO {
    public interface Views {
        interface Customer {}
        interface Employee {}
        interface Manager {}
        interface Admin {}
    }
    
    @JsonView({Views.Employee.class, Views.Manager.class, Views.Admin.class})
    private String phone;  // 完整手机号
    
    @JsonView({Views.Employee.class, Views.Manager.class, Views.Admin.class})
    private String idCard; // 身份证号
    
    @JsonView(Views.Customer.class)
    public String getMaskedPhone() {
        return phone != null ? phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2") : null;
    }
    
    @JsonView(Views.Customer.class)
    public String getMaskedIdCard() {
        return idCard != null ? idCard.replaceAll("(\\d{6})\\d{8}(\\d{4})", "$1********$2") : null;
    }
}
```

---

## 2. 代码规范和开发标准

### 2.1 项目结构规范 【必须】

```
src/
├── main/
│   ├── java/com/massage/{service-name}/
│   │   ├── controller/          # REST控制器
│   │   │   ├── admin/          # 总部端控制器
│   │   │   ├── manager/        # 店长端控制器
│   │   │   ├── employee/       # 员工端控制器
│   │   │   └── customer/       # 顾客端控制器
│   │   ├── service/            # 业务逻辑层
│   │   │   ├── impl/          # 实现类
│   │   │   └── facade/        # 门面服务
│   │   ├── repository/         # 数据访问层
│   │   ├── entity/            # 实体类
│   │   ├── dto/               # 数据传输对象
│   │   │   ├── request/       # 请求DTO
│   │   │   ├── response/      # 响应DTO
│   │   │   └── query/         # 查询DTO
│   │   ├── config/            # 配置类
│   │   ├── common/            # 通用工具类
│   │   │   ├── constant/      # 常量定义
│   │   │   ├── enums/         # 枚举类
│   │   │   ├── exception/     # 异常类
│   │   │   └── utils/         # 工具类
│   │   └── security/          # 安全相关
│   └── resources/
│       ├── application.yml    # 主配置文件
│       ├── application-{env}.yml  # 环境配置
│       ├── mapper/            # MyBatis映射文件
│       └── db/migration/      # 数据库迁移脚本
└── test/
    ├── java/                  # 测试代码
    └── resources/             # 测试资源
```

### 2.2 命名规范 【必须】

#### Java命名规范
```java
// 类名：PascalCase
public class AppointmentService {}
public class CustomerHealthRecord {}

// 方法名：camelCase，动词开头
public void createAppointment() {}
public List<Customer> findActiveCustomers() {}
public boolean validateAppointmentTime() {}

// 变量名：camelCase
private String customerName;
private LocalDateTime appointmentTime;

// 常量：UPPER_SNAKE_CASE
public static final String DEFAULT_TIME_ZONE = "Asia/Shanghai";
public static final int MAX_APPOINTMENT_DAYS = 30;

// 包名：全小写，点分隔
com.massage.appointment.service
com.massage.customer.repository
```

#### 数据库命名规范
```sql
-- 表名：snake_case，复数形式
CREATE TABLE health_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT NOT NULL,
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 字段名：snake_case
appointment_time, customer_name, phone_number

-- 索引名：idx_表名_字段名
INDEX idx_appointments_customer_time (customer_id, appointment_time)
INDEX idx_health_records_customer (customer_id)
```

### 2.3 API设计标准 【必须】

#### RESTful URL设计
```java
// 资源URL设计规范
@RestController
@RequestMapping("/api/v1")
public class AppointmentController {
    
    // GET /api/v1/appointments - 查询预约列表
    @GetMapping("/appointments")
    public ResponseEntity<PageResult<AppointmentDTO>> getAppointments() {}
    
    // GET /api/v1/appointments/{id} - 查询单个预约
    @GetMapping("/appointments/{id}")
    public ResponseEntity<AppointmentDTO> getAppointment(@PathVariable Long id) {}
    
    // POST /api/v1/appointments - 创建预约
    @PostMapping("/appointments")
    public ResponseEntity<AppointmentDTO> createAppointment(@RequestBody CreateAppointmentRequest request) {}
    
    // PUT /api/v1/appointments/{id} - 更新预约
    @PutMapping("/appointments/{id}")
    public ResponseEntity<AppointmentDTO> updateAppointment(@PathVariable Long id, @RequestBody UpdateAppointmentRequest request) {}
    
    // DELETE /api/v1/appointments/{id} - 删除预约
    @DeleteMapping("/appointments/{id}")
    public ResponseEntity<Void> deleteAppointment(@PathVariable Long id) {}
    
    // POST /api/v1/appointments/{id}/cancel - 取消预约（业务操作）
    @PostMapping("/appointments/{id}/cancel")
    public ResponseEntity<Void> cancelAppointment(@PathVariable Long id) {}
}
```

#### 统一响应格式 【必须】
```java
// 统一响应包装类
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiResponse<T> {
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;
    
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
            .code(200)
            .message("success")
            .data(data)
            .timestamp(System.currentTimeMillis())
            .build();
    }
    
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return ApiResponse.<T>builder()
            .code(code)
            .message(message)
            .timestamp(System.currentTimeMillis())
            .build();
    }
}

// 分页响应格式
@Data
@Builder
public class PageResult<T> {
    private Integer pageNum;
    private Integer pageSize;
    private Long total;
    private Integer pages;
    private List<T> list;
}
```

#### HTTP状态码规范 【必须】
```java
public enum HttpStatus {
    // 成功响应
    OK(200, "请求成功"),
    CREATED(201, "资源创建成功"),
    
    // 客户端错误
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    CONFLICT(409, "资源冲突"),
    
    // 服务器错误
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用");
}
```

---

## 3. 业务逻辑实现指导

### 3.1 预约业务核心逻辑 【必须】

```java
@Service
@Transactional
@Slf4j
public class AppointmentService {
    
    public AppointmentResult createAppointment(CreateAppointmentRequest request) {
        log.info("开始创建预约，客户ID: {}, 技师ID: {}", request.getCustomerId(), request.getTechnicianId());
        
        try {
            // 1. 参数验证
            validateAppointmentRequest(request);
            
            // 2. 验证预约时间可用性
            validateTimeSlotAvailability(request.getStoreId(), request.getAppointmentTime());
            
            // 3. 检查技师可用性和技能匹配
            validateTechnicianAvailability(request.getTechnicianId(), request.getAppointmentTime());
            validateTechnicianSkills(request.getTechnicianId(), request.getServiceItems());
            
            // 4. 检查房间资源
            validateRoomAvailability(request.getRoomId(), request.getAppointmentTime(), request.getDuration());
            
            // 5. 验证客户状态（会员余额、黑名单等）
            validateCustomerStatus(request.getCustomerId());
            
            // 6. 创建预约记录
            Appointment appointment = buildAppointment(request);
            appointment = appointmentRepository.save(appointment);
            
            // 7. 更新相关资源状态
            updateResourceStatus(appointment);
            
            // 8. 发送通知
            sendAppointmentNotifications(appointment);
            
            log.info("预约创建成功，预约ID: {}", appointment.getId());
            return AppointmentResult.success(appointment);
            
        } catch (BusinessException e) {
            log.warn("预约创建失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("预约创建异常", e);
            throw new SystemException("系统异常，请稍后重试");
        }
    }
    
    private void validateAppointmentRequest(CreateAppointmentRequest request) {
        if (request.getAppointmentTime().isBefore(LocalDateTime.now().plusHours(1))) {
            throw new BusinessException(ErrorCode.APPOINTMENT_TIME_TOO_EARLY);
        }
        
        if (request.getAppointmentTime().isAfter(LocalDateTime.now().plusDays(30))) {
            throw new BusinessException(ErrorCode.APPOINTMENT_TIME_TOO_LATE);
        }
    }
    
    private void validateTimeSlotAvailability(Long storeId, LocalDateTime appointmentTime) {
        boolean isAvailable = appointmentRepository.isTimeSlotAvailable(storeId, appointmentTime);
        if (!isAvailable) {
            throw new BusinessException(ErrorCode.TIME_SLOT_NOT_AVAILABLE);
        }
    }
}
```

### 3.2 股东分红计算逻辑 【必须】

```java
@Service
@Slf4j
public class DividendCalculationService {
    
    public DividendDistribution calculateMonthlyDividend(Long storeId, YearMonth period) {
        log.info("开始计算股东分红，门店ID: {}, 期间: {}", storeId, period);
        
        // 1. 获取财务数据
        FinancialStatement statement = financialService.getFinancialStatement(storeId, period);
        
        // 2. 计算可分配利润
        BigDecimal distributableProfit = calculateDistributableProfit(statement);
        
        // 3. 获取股东信息
        List<ShareholderInfo> shareholders = shareholderService.getStoreShareholders(storeId);
        
        // 4. 按股权比例分配
        List<ShareholderDividend> dividends = calculateDividends(distributableProfit, shareholders, period);
        
        // 5. 创建分红记录
        DividendDistribution distribution = createDividendDistribution(storeId, period, dividends);
        
        log.info("股东分红计算完成，总分红金额: {}", distributableProfit);
        return distribution;
    }
    
    private BigDecimal calculateDistributableProfit(FinancialStatement statement) {
        BigDecimal netProfit = statement.getNetProfit();
        BigDecimal reservedFunds = statement.getReservedFunds();
        BigDecimal operatingReserve = statement.getOperatingReserve();
        
        return netProfit.subtract(reservedFunds).subtract(operatingReserve);
    }
    
    private List<ShareholderDividend> calculateDividends(
            BigDecimal distributableProfit, 
            List<ShareholderInfo> shareholders, 
            YearMonth period) {
        
        return shareholders.stream()
            .map(shareholder -> {
                BigDecimal dividend = distributableProfit
                    .multiply(shareholder.getShareRatio())
                    .setScale(2, RoundingMode.HALF_UP);
                    
                return ShareholderDividend.builder()
                    .shareholderId(shareholder.getId())
                    .shareholderName(shareholder.getName())
                    .shareRatio(shareholder.getShareRatio())
                    .dividendAmount(dividend)
                    .period(period)
                    .status(DividendStatus.CALCULATED)
                    .build();
            })
            .collect(Collectors.toList());
    }
}
```

### 3.3 中医专业化功能实现 【推荐】

#### 经络穴位数据结构
```java
@Entity
@Table(name = "acupoint_records")
public class AcupointRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "customer_id", nullable = false)
    private Long customerId;
    
    @Column(name = "meridian_type")
    @Enumerated(EnumType.STRING)
    private MeridianType meridianType;
    
    @Column(name = "acupoint_code", length = 20)
    private String acupointCode; // 如: LI4-合谷, ST36-足三里
    
    @Column(name = "body_position", columnDefinition = "JSON")
    @Convert(converter = BodyPositionConverter.class)
    private BodyPosition position;
    
    @Column(name = "symptom_tags", columnDefinition = "JSON")
    @Convert(converter = StringListConverter.class)
    private List<String> symptomTags;
    
    @Column(name = "pressure_level")
    private Integer pressureLevel; // 1-10级压力
    
    @Column(name = "treatment_effect")
    @Enumerated(EnumType.STRING)
    private TreatmentEffect effect;
}

// 经络类型枚举
public enum MeridianType {
    LUNG("手太阴肺经", "LU"), 
    LARGE_INTESTINE("手阳明大肠经", "LI"),
    STOMACH("足阳明胃经", "ST"),
    SPLEEN("足太阴脾经", "SP"),
    HEART("手少阴心经", "HT"),
    SMALL_INTESTINE("手太阳小肠经", "SI"),
    BLADDER("足太阳膀胱经", "BL"),
    KIDNEY("足少阴肾经", "KI"),
    PERICARDIUM("手厥阴心包经", "PC"),
    TRIPLE_HEATER("手少阳三焦经", "TH"),
    GALLBLADDER("足少阳胆经", "GB"),
    LIVER("足厥阴肝经", "LV");
    
    private final String chineseName;
    private final String code;
}
```

#### 体质辨识算法
```java
@Service
public class ConstitutionAnalysisService {
    
    public ConstitutionResult analyzeConstitution(ConstitutionSurvey survey) {
        // 九种体质评分算法
        Map<ConstitutionType, Integer> scores = new HashMap<>();
        
        // 平和质评分
        scores.put(ConstitutionType.BALANCED, calculateBalancedScore(survey));
        // 气虚质评分  
        scores.put(ConstitutionType.QI_DEFICIENCY, calculateQiDeficiencyScore(survey));
        // 阳虚质评分
        scores.put(ConstitutionType.YANG_DEFICIENCY, calculateYangDeficiencyScore(survey));
        // 阴虚质评分
        scores.put(ConstitutionType.YIN_DEFICIENCY, calculateYinDeficiencyScore(survey));
        // 痰湿质评分
        scores.put(ConstitutionType.PHLEGM_DAMPNESS, calculatePhlegmDampnessScore(survey));
        // 湿热质评分
        scores.put(ConstitutionType.DAMP_HEAT, calculateDampHeatScore(survey));
        // 血瘀质评分
        scores.put(ConstitutionType.BLOOD_STASIS, calculateBloodStasisScore(survey));
        // 气郁质评分
        scores.put(ConstitutionType.QI_STAGNATION, calculateQiStagnationScore(survey));
        // 特禀质评分
        scores.put(ConstitutionType.SPECIAL_DIATHESIS, calculateSpecialDiathesisScore(survey));
        
        ConstitutionType primaryConstitution = getPrimaryConstitution(scores);
        List<String> recommendations = generateRecommendations(primaryConstitution, scores);
        
        return ConstitutionResult.builder()
            .primaryConstitution(primaryConstitution)
            .scores(scores)
            .recommendations(recommendations)
            .analysisDate(LocalDateTime.now())
            .build();
    }
    
    private Integer calculateBalancedScore(ConstitutionSurvey survey) {
        // 平和质评分算法：精力充沛、睡眠良好、食欲正常等
        int score = 0;
        if (survey.getEnergyLevel() >= 4) score += 10;
        if (survey.getSleepQuality() >= 4) score += 10;
        if (survey.getAppetite() >= 4) score += 10;
        // ... 其他评分逻辑
        return Math.min(score, 100);
    }
}
```

### 3.4 智能排班算法 【推荐】

```java
@Service
public class IntelligentSchedulingService {
    
    public List<ScheduleSuggestion> suggestOptimalSchedule(ScheduleRequest request) {
        // 1. 获取可用技师
        List<Technician> availableTechnicians = getAvailableTechnicians(request);
        
        // 2. 应用约束条件过滤
        List<Technician> validTechnicians = applyConstraints(availableTechnicians, request);
        
        // 3. 计算匹配度评分
        List<ScheduleSuggestion> suggestions = validTechnicians.stream()
            .map(technician -> {
                double score = calculateMatchScore(technician, request);
                return ScheduleSuggestion.builder()
                    .technician(technician)
                    .matchScore(score)
                    .estimatedDuration(calculateDuration(request.getServices()))
                    .suggestedRoom(findOptimalRoom(technician, request))
                    .build();
            })
            .sorted(Comparator.comparing(ScheduleSuggestion::getMatchScore).reversed())
            .limit(5)
            .collect(Collectors.toList());
            
        return suggestions;
    }
    
    private double calculateMatchScore(Technician technician, ScheduleRequest request) {
        // 技能匹配度 (50%)
        double skillScore = calculateSkillMatch(technician, request.getServices());
        
        // 经验等级 (30%)
        double experienceScore = technician.getExperienceLevel() * 0.3;
        
        // 客户偏好 (20%)
        double customerPreferenceScore = calculateCustomerPreference(technician, request.getCustomerId());
        
        return skillScore * 0.5 + experienceScore + customerPreferenceScore;
    }
    
    private List<Technician> applyConstraints(List<Technician> technicians, ScheduleRequest request) {
        return technicians.stream()
            .filter(technician -> {
                // 工作时长约束
                if (calculateDailyHours(technician, request.getDate()) >= 8) {
                    return false;
                }
                
                // 技能匹配约束
                if (!isSkillMatched(technician, request.getServices())) {
                    return false;
                }
                
                // 客户性别偏好约束
                if (!isGenderPreferenceMatched(technician, request.getCustomerId())) {
                    return false;
                }
                
                return true;
            })
            .collect(Collectors.toList());
    }
}
```

### 3.5 营销自动化引擎 【推荐】

#### 规则引擎实现
```java
@Component
public class MarketingRuleEngine {

    public void executeMarketingRules(CustomerEvent event) {
        List<MarketingRule> applicableRules = ruleRepository
            .findByEventTypeAndStatus(event.getEventType(), RuleStatus.ACTIVE);

        for (MarketingRule rule : applicableRules) {
            if (evaluateConditions(rule.getConditions(), event)) {
                executeActions(rule.getActions(), event.getCustomerId());
            }
        }
    }

    private boolean evaluateConditions(List<RuleCondition> conditions, CustomerEvent event) {
        return conditions.stream().allMatch(condition -> {
            switch (condition.getType()) {
                case LAST_VISIT_DAYS:
                    return event.getDaysSinceLastVisit() >= condition.getValue();
                case TOTAL_CONSUMPTION:
                    return event.getTotalConsumption().compareTo(condition.getAmount()) >= 0;
                case MEMBER_LEVEL:
                    return event.getMemberLevel().ordinal() >= condition.getLevel().ordinal();
                default:
                    return false;
            }
        });
    }

    private void executeActions(List<RuleAction> actions, Long customerId) {
        for (RuleAction action : actions) {
            switch (action.getType()) {
                case SEND_COUPON:
                    couponService.issueCoupon(customerId, action.getCouponTemplate());
                    break;
                case SEND_SMS:
                    smsService.sendMessage(customerId, action.getMessageTemplate());
                    break;
                case UPGRADE_MEMBER:
                    memberService.upgradeMemberLevel(customerId, action.getTargetLevel());
                    break;
                case SCHEDULE_CALLBACK:
                    callbackService.scheduleCallback(customerId, action.getCallbackTime());
                    break;
            }
        }
    }
}
```

#### 客户生命周期管理
```java
@Service
public class CustomerLifecycleService {

    @EventListener
    public void handleCustomerRegistration(CustomerRegisteredEvent event) {
        // 新客户欢迎流程
        marketingService.sendWelcomeMessage(event.getCustomerId());
        couponService.issueWelcomeCoupon(event.getCustomerId());

        // 设置新客户标签
        customerTagService.addTag(event.getCustomerId(), "新客户");

        // 安排首次体验预约提醒
        scheduleService.scheduleFirstVisitReminder(event.getCustomerId());
    }

    @EventListener
    public void handleCustomerChurn(CustomerChurnEvent event) {
        // 流失客户挽回流程
        marketingService.sendRetentionOffer(event.getCustomerId());
        scheduleFollowUpCall(event.getCustomerId());

        // 分析流失原因
        churnAnalysisService.analyzeChurnReason(event.getCustomerId());
    }

    @EventListener
    public void handleCustomerUpgrade(CustomerUpgradeEvent event) {
        // 会员升级庆祝
        marketingService.sendUpgradeCongratulation(event.getCustomerId());
        couponService.issueUpgradeBonus(event.getCustomerId(), event.getNewLevel());
    }

    @Scheduled(cron = "0 0 9 * * ?") // 每天9点执行
    public void detectChurnRisk() {
        List<Customer> riskCustomers = customerAnalysisService.identifyChurnRisk();
        riskCustomers.forEach(customer -> {
            applicationEventPublisher.publishEvent(
                new CustomerChurnRiskEvent(customer.getId())
            );
        });
    }

    @Scheduled(cron = "0 0 10 * * ?") // 每天10点执行
    public void detectUpgradeOpportunity() {
        List<Customer> upgradeCandidate = customerAnalysisService.identifyUpgradeOpportunity();
        upgradeCandidate.forEach(customer -> {
            marketingService.sendUpgradeInvitation(customer.getId());
        });
    }
}
```

#### 精准营销算法
```java
@Service
public class PrecisionMarketingService {

    public List<MarketingRecommendation> generateRecommendations(Long customerId) {
        Customer customer = customerService.getById(customerId);
        CustomerProfile profile = customerProfileService.getProfile(customerId);

        List<MarketingRecommendation> recommendations = new ArrayList<>();

        // 基于消费历史推荐
        recommendations.addAll(generateConsumptionBasedRecommendations(customer, profile));

        // 基于偏好推荐
        recommendations.addAll(generatePreferenceBasedRecommendations(customer, profile));

        // 基于生命周期阶段推荐
        recommendations.addAll(generateLifecycleBasedRecommendations(customer, profile));

        // 基于相似客户推荐
        recommendations.addAll(generateCollaborativeRecommendations(customer, profile));

        return recommendations.stream()
            .sorted(Comparator.comparing(MarketingRecommendation::getScore).reversed())
            .limit(5)
            .collect(Collectors.toList());
    }

    private List<MarketingRecommendation> generateConsumptionBasedRecommendations(
            Customer customer, CustomerProfile profile) {

        List<ServiceConsumption> recentConsumptions = consumptionService
            .getRecentConsumptions(customer.getId(), 6); // 最近6个月

        Map<String, Long> serviceFrequency = recentConsumptions.stream()
            .collect(Collectors.groupingBy(
                ServiceConsumption::getServiceType,
                Collectors.counting()
            ));

        return serviceFrequency.entrySet().stream()
            .map(entry -> {
                String serviceType = entry.getKey();
                Long frequency = entry.getValue();

                double score = calculateRecommendationScore(serviceType, frequency, profile);

                return MarketingRecommendation.builder()
                    .customerId(customer.getId())
                    .recommendationType(RecommendationType.SERVICE_REPEAT)
                    .serviceType(serviceType)
                    .score(score)
                    .reason("基于您的消费偏好推荐")
                    .build();
            })
            .collect(Collectors.toList());
    }
}
```

---

## 4. 数据安全和隐私保护

### 4.1 健康档案敏感数据处理 【必须】

#### 数据加密存储
```java
@Service
public class HealthRecordEncryptionService {
    
    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 16;
    
    @Value("${app.security.health-record.encryption-key}")
    private String encryptionKey;
    
    public String encryptHealthData(String plainText) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(
                Base64.getDecoder().decode(encryptionKey), "AES");
            
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            byte[] iv = new byte[GCM_IV_LENGTH];
            SecureRandom.getInstanceStrong().nextBytes(iv);
            
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmSpec);
            
            byte[] encryptedData = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            
            // 将IV和加密数据组合
            byte[] encryptedWithIv = new byte[GCM_IV_LENGTH + encryptedData.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedData, 0, encryptedWithIv, GCM_IV_LENGTH, encryptedData.length);
            
            return Base64.getEncoder().encodeToString(encryptedWithIv);
            
        } catch (Exception e) {
            throw new SecurityException("健康数据加密失败", e);
        }
    }
    
    public String decryptHealthData(String encryptedText) {
        try {
            byte[] encryptedWithIv = Base64.getDecoder().decode(encryptedText);
            
            // 分离IV和加密数据
            byte[] iv = new byte[GCM_IV_LENGTH];
            byte[] encryptedData = new byte[encryptedWithIv.length - GCM_IV_LENGTH];
            System.arraycopy(encryptedWithIv, 0, iv, 0, GCM_IV_LENGTH);
            System.arraycopy(encryptedWithIv, GCM_IV_LENGTH, encryptedData, 0, encryptedData.length);
            
            SecretKeySpec secretKey = new SecretKeySpec(
                Base64.getDecoder().decode(encryptionKey), "AES");
            
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec);
            
            byte[] decryptedData = cipher.doFinal(encryptedData);
            return new String(decryptedData, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            throw new SecurityException("健康数据解密失败", e);
        }
    }
}
```

#### 访问控制和审计日志
```java
@Service
@Slf4j
public class HealthRecordService {
    
    @PreAuthorize("hasPermission(#customerId, 'HEALTH_RECORD', 'READ')")
    public HealthRecordDTO getHealthRecord(Long customerId) {
        // 记录访问审计日志
        auditService.logHealthRecordAccess(
            SecurityContextHolder.getContext().getAuthentication().getName(),
            customerId,
            "READ",
            getClientIP()
        );
        
        HealthRecord record = healthRecordRepository.findByCustomerId(customerId)
            .orElseThrow(() -> new NotFoundException("健康档案不存在"));
            
        // 解密敏感数据
        String decryptedData = encryptionService.decryptHealthData(record.getEncryptedData());
        
        return HealthRecordDTO.builder()
            .customerId(customerId)
            .healthData(decryptedData)
            .lastUpdated(record.getUpdatedTime())
            .build();
    }
    
    @PreAuthorize("hasPermission(#customerId, 'HEALTH_RECORD', 'WRITE')")
    public void updateHealthRecord(Long customerId, UpdateHealthRecordRequest request) {
        // 二次验证敏感操作
        if (!verificationService.verifySecondaryAuth(request.getVerificationCode())) {
            throw new SecurityException("二次验证失败");
        }
        
        // 加密敏感数据
        String encryptedData = encryptionService.encryptHealthData(request.getHealthData());
        
        HealthRecord record = healthRecordRepository.findByCustomerId(customerId)
            .orElse(new HealthRecord());
            
        record.setCustomerId(customerId);
        record.setEncryptedData(encryptedData);
        record.setRetentionPeriod(calculateRetentionPeriod());
        
        healthRecordRepository.save(record);
        
        // 记录修改审计日志
        auditService.logHealthRecordAccess(
            SecurityContextHolder.getContext().getAuthentication().getName(),
            customerId,
            "UPDATE",
            getClientIP()
        );
    }
}
```

### 4.2 数据传输安全 【必须】

#### JWT Token验证
```java
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String token = extractToken(request);
        
        if (token != null && jwtTokenProvider.validateToken(token)) {
            Authentication auth = jwtTokenProvider.getAuthentication(token);
            SecurityContextHolder.getContext().setAuthentication(auth);
            
            // 设置租户上下文
            String tenantId = jwtTokenProvider.getTenantId(token);
            TenantContext.setCurrentTenantId(Long.valueOf(tenantId));
        }
        
        filterChain.doFilter(request, response);
    }
    
    private String extractToken(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
```

#### 接口安全防护
```java
@RestController
@RequestMapping("/api/v1/health-records")
@Slf4j
public class HealthRecordController {
    
    @PostMapping
    @PreAuthorize("hasPermission(#customerId, 'HEALTH_RECORD', 'WRITE')")
    @RateLimiter(name = "health-record-api", fallbackMethod = "rateLimitFallback")
    public ResponseEntity<HealthRecordDTO> createHealthRecord(
            @PathVariable Long customerId,
            @Valid @RequestBody CreateHealthRecordRequest request,
            HttpServletRequest httpRequest) {
        
        // IP白名单检查
        String clientIP = getClientIP(httpRequest);
        if (!ipWhitelistService.isAllowed(clientIP)) {
            log.warn("非法IP访问健康档案接口: {}", clientIP);
            throw new SecurityException("IP地址不在白名单中");
        }
        
        // 敏感操作二次验证
        if (!verificationService.verifySecondaryAuth(request.getVerificationCode())) {
            log.warn("健康档案创建二次验证失败，用户: {}", getCurrentUserId());
            throw new SecurityException("二次验证失败");
        }
        
        HealthRecordDTO result = healthRecordService.createHealthRecord(customerId, request);
        return ResponseEntity.ok(result);
    }
    
    public ResponseEntity<String> rateLimitFallback(Exception ex) {
        log.warn("健康档案接口触发限流: {}", ex.getMessage());
        return ResponseEntity.status(429).body("请求过于频繁，请稍后重试");
    }
    
    private String getClientIP(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }
}
```

### 4.3 数据生命周期管理 【必须】

```java
@Entity
@Table(name = "health_records")
public class HealthRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "customer_id", nullable = false)
    private Long customerId;
    
    @Column(name = "encrypted_data", columnDefinition = "TEXT")
    private String encryptedData;
    
    @Column(name = "data_classification")
    @Enumerated(EnumType.STRING)
    private DataClassification classification; // 数据分类
    
    @Column(name = "retention_period")
    private Integer retentionPeriod; // 数据保留期限(天)
    
    @Column(name = "expiry_date")
    private LocalDateTime expiryDate; // 过期时间
    
    @Column(name = "created_time")
    private LocalDateTime createdTime;
    
    @Column(name = "updated_time")
    private LocalDateTime updatedTime;
    
    @PrePersist
    public void prePersist() {
        this.createdTime = LocalDateTime.now();
        this.updatedTime = LocalDateTime.now();
        
        // 根据数据分类设置保留期限
        if (this.classification == DataClassification.SENSITIVE_HEALTH) {
            this.retentionPeriod = 2555; // 7年
        } else if (this.classification == DataClassification.GENERAL_HEALTH) {
            this.retentionPeriod = 1095; // 3年
        }
        
        this.expiryDate = this.createdTime.plusDays(this.retentionPeriod);
    }
}

// 数据分类枚举
public enum DataClassification {
    SENSITIVE_HEALTH("敏感健康数据", 2555),
    GENERAL_HEALTH("一般健康数据", 1095),
    BASIC_INFO("基础信息", 365);
    
    private final String description;
    private final int defaultRetentionDays;
}

// 数据清理定时任务
@Component
@Slf4j
public class DataRetentionScheduler {
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanExpiredData() {
        log.info("开始执行过期数据清理任务");
        
        LocalDateTime now = LocalDateTime.now();
        
        // 查找过期的健康档案
        List<HealthRecord> expiredRecords = healthRecordRepository
            .findByExpiryDateBefore(now);
            
        for (HealthRecord record : expiredRecords) {
            // 记录删除审计日志
            auditService.logDataDeletion(record.getId(), "RETENTION_POLICY", now);
            
            // 删除记录
            healthRecordRepository.delete(record);
        }
        
        log.info("过期数据清理完成，删除记录数: {}", expiredRecords.size());
    }
}
```

### 4.4 接口安全防护加固 【必须】

#### API安全防护
```java
@RestController
@RequestMapping("/api/v1/health-records")
@Slf4j
public class HealthRecordController {

    @PostMapping
    @PreAuthorize("hasPermission(#customerId, 'HEALTH_RECORD', 'WRITE')")
    @RateLimiter(name = "health-record-api", fallbackMethod = "rateLimitFallback")
    public ResponseEntity<HealthRecordDTO> createHealthRecord(
            @PathVariable Long customerId,
            @Valid @RequestBody CreateHealthRecordRequest request,
            HttpServletRequest httpRequest) {

        // IP白名单检查
        String clientIP = getClientIP(httpRequest);
        if (!ipWhitelistService.isAllowed(clientIP)) {
            log.warn("非法IP访问健康档案接口: {}", clientIP);
            throw new SecurityException("IP地址不在白名单中");
        }

        // 敏感操作二次验证
        if (!verificationService.verifySecondaryAuth(request.getVerificationCode())) {
            log.warn("健康档案创建二次验证失败，用户: {}", getCurrentUserId());
            throw new SecurityException("二次验证失败");
        }

        // 请求频率检查
        if (!rateLimitService.isAllowed(getCurrentUserId(), "health_record_create")) {
            log.warn("用户请求频率过高: {}", getCurrentUserId());
            throw new SecurityException("请求过于频繁");
        }

        HealthRecordDTO result = healthRecordService.createHealthRecord(customerId, request);
        return ResponseEntity.ok(result);
    }

    public ResponseEntity<String> rateLimitFallback(Exception ex) {
        log.warn("健康档案接口触发限流: {}", ex.getMessage());
        return ResponseEntity.status(429).body("请求过于频繁，请稍后重试");
    }

    private String getClientIP(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        String xRealIP = request.getHeader("X-Real-IP");
        if (xRealIP != null && !xRealIP.isEmpty()) {
            return xRealIP;
        }
        return request.getRemoteAddr();
    }
}
```

#### 数据访问审计增强
```java
@Component
@Slf4j
public class SecurityAuditService {

    @EventListener
    public void handleHealthRecordAccess(HealthRecordAccessEvent event) {
        // 记录详细的访问审计日志
        SecurityAuditLog auditLog = SecurityAuditLog.builder()
            .userId(event.getUserId())
            .userName(event.getUserName())
            .userRole(event.getUserRole())
            .resourceType("HEALTH_RECORD")
            .resourceId(event.getCustomerId().toString())
            .action(event.getAction())
            .clientIP(event.getClientIP())
            .userAgent(event.getUserAgent())
            .accessTime(LocalDateTime.now())
            .success(event.isSuccess())
            .failureReason(event.getFailureReason())
            .build();

        auditLogRepository.save(auditLog);

        // 异常访问检测
        if (detectAbnormalAccess(event)) {
            alertService.sendSecurityAlert(
                "检测到异常访问行为",
                String.format("用户 %s 在 %s 时间内频繁访问健康档案",
                    event.getUserName(), "5分钟")
            );
        }
    }

    private boolean detectAbnormalAccess(HealthRecordAccessEvent event) {
        // 检查5分钟内的访问频率
        LocalDateTime fiveMinutesAgo = LocalDateTime.now().minusMinutes(5);
        long recentAccessCount = auditLogRepository.countByUserIdAndResourceTypeAndAccessTimeAfter(
            event.getUserId(), "HEALTH_RECORD", fiveMinutesAgo);

        return recentAccessCount > 10; // 5分钟内超过10次访问视为异常
    }

    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void generateSecurityReport() {
        LocalDate yesterday = LocalDate.now().minusDays(1);

        // 统计昨日安全事件
        SecurityReport report = SecurityReport.builder()
            .reportDate(yesterday)
            .totalAccess(auditLogRepository.countByAccessDate(yesterday))
            .failedAccess(auditLogRepository.countByAccessDateAndSuccess(yesterday, false))
            .suspiciousAccess(auditLogRepository.countSuspiciousAccess(yesterday))
            .topAccessUsers(auditLogRepository.findTopAccessUsers(yesterday, 10))
            .build();

        securityReportService.saveReport(report);

        // 如果有异常情况，发送告警
        if (report.getSuspiciousAccess() > 0) {
            alertService.sendSecurityAlert(
                "发现可疑访问行为",
                String.format("昨日共发现 %d 次可疑访问", report.getSuspiciousAccess())
            );
        }
    }
}
```

#### 数据脱敏增强
```java
@Component
public class DataMaskingService {

    public <T> T maskSensitiveData(T data, ClientType clientType) {
        if (data == null) return null;

        try {
            T maskedData = (T) data.getClass().getDeclaredConstructor().newInstance();
            Field[] fields = data.getClass().getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(data);

                if (field.isAnnotationPresent(SensitiveData.class)) {
                    SensitiveData annotation = field.getAnnotation(SensitiveData.class);
                    Object maskedValue = maskFieldValue(value, annotation.type(), clientType);
                    field.set(maskedData, maskedValue);
                } else {
                    field.set(maskedData, value);
                }
            }

            return maskedData;
        } catch (Exception e) {
            log.error("数据脱敏失败", e);
            throw new SecurityException("数据脱敏失败");
        }
    }

    private Object maskFieldValue(Object value, SensitiveType type, ClientType clientType) {
        if (value == null) return null;

        String stringValue = value.toString();

        switch (type) {
            case PHONE:
                return maskPhone(stringValue, clientType);
            case ID_CARD:
                return maskIdCard(stringValue, clientType);
            case NAME:
                return maskName(stringValue, clientType);
            case ADDRESS:
                return maskAddress(stringValue, clientType);
            case HEALTH_INFO:
                return maskHealthInfo(stringValue, clientType);
            default:
                return value;
        }
    }

    private String maskPhone(String phone, ClientType clientType) {
        if (phone == null || phone.length() < 11) return phone;

        switch (clientType) {
            case CUSTOMER:
                return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
            case EMPLOYEE:
            case CASHIER:
                return phone.replaceAll("(\\d{3})\\d{2}(\\d{6})", "$1**$2");
            case MANAGER:
            case SHAREHOLDER:
            case ADMIN:
                return phone; // 完整显示
            default:
                return "***********";
        }
    }

    private String maskIdCard(String idCard, ClientType clientType) {
        if (idCard == null || idCard.length() < 18) return idCard;

        switch (clientType) {
            case CUSTOMER:
                return idCard.replaceAll("(\\d{6})\\d{8}(\\d{4})", "$1********$2");
            case EMPLOYEE:
            case CASHIER:
                return idCard.replaceAll("(\\d{6})\\d{6}(\\d{6})", "$1******$2");
            case MANAGER:
            case SHAREHOLDER:
            case ADMIN:
                return idCard; // 完整显示
            default:
                return "******************";
        }
    }
}

// 敏感数据注解
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SensitiveData {
    SensitiveType type();
}

public enum SensitiveType {
    PHONE, ID_CARD, NAME, ADDRESS, HEALTH_INFO
}
```

---

## 5. 测试和质量保证

### 5.1 测试覆盖率要求 【必须】

#### 单元测试规范
```java
@ExtendWith(MockitoExtension.class)
@DisplayName("预约服务测试")
class AppointmentServiceTest {
    
    @Mock
    private AppointmentRepository appointmentRepository;
    
    @Mock
    private TechnicianService technicianService;
    
    @Mock
    private NotificationService notificationService;
    
    @InjectMocks
    private AppointmentService appointmentService;
    
    @Test
    @DisplayName("应该成功创建预约")
    void shouldCreateAppointmentSuccessfully() {
        // Given
        CreateAppointmentRequest request = CreateAppointmentRequest.builder()
            .customerId(1L)
            .technicianId(1L)
            .storeId(1L)
            .appointmentTime(LocalDateTime.now().plusDays(1))
            .serviceItems(Arrays.asList("按摩", "推拿"))
            .build();
            
        Technician technician = Technician.builder()
            .id(1L)
            .name("张技师")
            .skills(Arrays.asList("按摩", "推拿"))
            .build();
            
        when(technicianService.findById(1L)).thenReturn(technician);
        when(appointmentRepository.isTimeSlotAvailable(any(), any())).thenReturn(true);
        when(appointmentRepository.save(any())).thenAnswer(invocation -> {
            Appointment appointment = invocation.getArgument(0);
            appointment.setId(1L);
            return appointment;
        });
        
        // When
        AppointmentResult result = appointmentService.createAppointment(request);
        
        // Then
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getAppointment().getId()).isEqualTo(1L);
        
        verify(appointmentRepository).save(any(Appointment.class));
        verify(notificationService).sendAppointmentConfirmation(any());
    }
    
    @Test
    @DisplayName("当时间段不可用时应该抛出异常")
    void shouldThrowExceptionWhenTimeSlotNotAvailable() {
        // Given
        CreateAppointmentRequest request = CreateAppointmentRequest.builder()
            .customerId(1L)
            .technicianId(1L)
            .storeId(1L)
            .appointmentTime(LocalDateTime.now().plusDays(1))
            .build();
            
        when(appointmentRepository.isTimeSlotAvailable(any(), any())).thenReturn(false);
        
        // When & Then
        assertThatThrownBy(() -> appointmentService.createAppointment(request))
            .isInstanceOf(BusinessException.class)
            .hasMessage("时间段不可用");
    }
}
```

#### 集成测试规范
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = "classpath:application-test.yml")
@Transactional
@DisplayName("预约API集成测试")
class AppointmentControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Autowired
    private AppointmentRepository appointmentRepository;
    
    @Test
    @DisplayName("应该成功创建预约并返回201状态码")
    void shouldCreateAppointmentAndReturn201() {
        // Given
        CreateAppointmentRequest request = CreateAppointmentRequest.builder()
            .customerId(1L)
            .technicianId(1L)
            .storeId(1L)
            .appointmentTime(LocalDateTime.now().plusDays(1))
            .serviceItems(Arrays.asList("按摩"))
            .build();
            
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(getValidJwtToken());
        
        HttpEntity<CreateAppointmentRequest> entity = new HttpEntity<>(request, headers);
        
        // When
        ResponseEntity<ApiResponse<AppointmentDTO>> response = restTemplate.exchange(
            "/api/v1/appointments",
            HttpMethod.POST,
            entity,
            new ParameterizedTypeReference<ApiResponse<AppointmentDTO>>() {}
        );
        
        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CREATED);
        assertThat(response.getBody().getCode()).isEqualTo(201);
        assertThat(response.getBody().getData().getCustomerId()).isEqualTo(1L);
        
        // 验证数据库中的记录
        List<Appointment> appointments = appointmentRepository.findByCustomerId(1L);
        assertThat(appointments).hasSize(1);
    }
}
```

### 5.2 性能测试标准 【必须】

#### 性能测试配置
```java
@Component
@Slf4j
public class PerformanceTestConfig {
    
    // API响应时间要求
    public static final int CORE_API_MAX_RESPONSE_TIME = 500; // ms
    public static final int QUERY_API_MAX_RESPONSE_TIME = 200; // ms
    
    // 并发支持要求
    public static final int MIN_CONCURRENT_USERS = 1000;
    public static final int TARGET_CONCURRENT_USERS = 2000;
    
    // 数据库查询性能要求
    public static final int DB_QUERY_MAX_TIME = 100; // ms
    public static final int DB_TRANSACTION_MAX_TIME = 500; // ms
}

@Component
public class PerformanceMonitor {
    
    private final MeterRegistry meterRegistry;
    
    @EventListener
    public void handleAppointmentCreated(AppointmentCreatedEvent event) {
        // 记录预约创建指标
        meterRegistry.counter("appointment.created", 
            "store", event.getStoreId().toString()).increment();
            
        // 记录响应时间
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("appointment.creation.time")
            .register(meterRegistry));
    }
    
    @Scheduled(fixedRate = 60000) // 每分钟检查
    public void checkPerformanceMetrics() {
        // 检查API响应时间
        double avgResponseTime = getAverageResponseTime();
        if (avgResponseTime > CORE_API_MAX_RESPONSE_TIME) {
            alertService.sendAlert(AlertType.PERFORMANCE, 
                "API响应时间超标: " + avgResponseTime + "ms");
        }
        
        // 检查数据库连接池
        int activeConnections = getActiveDbConnections();
        int maxConnections = getMaxDbConnections();
        if (activeConnections > maxConnections * 0.8) {
            alertService.sendAlert(AlertType.RESOURCE, 
                "数据库连接池使用率过高: " + (activeConnections * 100 / maxConnections) + "%");
        }
    }
}
```

### 5.3 性能监控和预警 【必须】

#### 关键指标监控
```java
@Component
@Slf4j
public class PerformanceMonitor {

    private final MeterRegistry meterRegistry;
    private final AlertService alertService;

    @EventListener
    public void handleAppointmentCreated(AppointmentCreatedEvent event) {
        // 记录预约创建指标
        meterRegistry.counter("appointment.created",
            "store", event.getStoreId().toString()).increment();

        // 记录响应时间
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("appointment.creation.time")
            .register(meterRegistry));
    }

    @EventListener
    public void handleApiRequest(ApiRequestEvent event) {
        // 记录API调用指标
        meterRegistry.counter("api.requests",
            "endpoint", event.getEndpoint(),
            "method", event.getMethod(),
            "status", String.valueOf(event.getStatusCode())
        ).increment();

        // 记录响应时间
        meterRegistry.timer("api.response.time",
            "endpoint", event.getEndpoint()
        ).record(event.getResponseTime(), TimeUnit.MILLISECONDS);
    }

    @Scheduled(fixedRate = 60000) // 每分钟检查
    public void checkSystemHealth() {
        // 检查数据库连接
        if (!databaseHealthCheck()) {
            alertService.sendAlert(AlertType.DATABASE, "数据库连接异常");
        }

        // 检查Redis连接
        if (!redisHealthCheck()) {
            alertService.sendAlert(AlertType.CACHE, "Redis连接异常");
        }

        // 检查API响应时间
        double avgResponseTime = getAverageResponseTime();
        if (avgResponseTime > PerformanceThreshold.API_RESPONSE_TIME_MAX) {
            alertService.sendAlert(AlertType.PERFORMANCE,
                String.format("API响应时间超标: %.2fms", avgResponseTime));
        }

        // 检查数据库连接池
        int activeConnections = getActiveDbConnections();
        int maxConnections = getMaxDbConnections();
        double connectionUsageRate = (double) activeConnections / maxConnections;

        if (connectionUsageRate > 0.8) {
            alertService.sendAlert(AlertType.RESOURCE,
                String.format("数据库连接池使用率过高: %.1f%%", connectionUsageRate * 100));
        }

        // 检查内存使用率
        MemoryUsage heapMemory = ManagementFactory.getMemoryMXBean().getHeapMemoryUsage();
        double memoryUsageRate = (double) heapMemory.getUsed() / heapMemory.getMax();

        if (memoryUsageRate > 0.85) {
            alertService.sendAlert(AlertType.RESOURCE,
                String.format("内存使用率过高: %.1f%%", memoryUsageRate * 100));
        }
    }

    @Scheduled(cron = "0 */5 * * * ?") // 每5分钟检查
    public void checkBusinessMetrics() {
        // 检查预约成功率
        double appointmentSuccessRate = calculateAppointmentSuccessRate();
        if (appointmentSuccessRate < 0.95) {
            alertService.sendAlert(AlertType.BUSINESS,
                String.format("预约成功率过低: %.1f%%", appointmentSuccessRate * 100));
        }

        // 检查支付成功率
        double paymentSuccessRate = calculatePaymentSuccessRate();
        if (paymentSuccessRate < 0.98) {
            alertService.sendAlert(AlertType.BUSINESS,
                String.format("支付成功率过低: %.1f%%", paymentSuccessRate * 100));
        }

        // 检查系统错误率
        double errorRate = calculateSystemErrorRate();
        if (errorRate > 0.01) {
            alertService.sendAlert(AlertType.SYSTEM,
                String.format("系统错误率过高: %.2f%%", errorRate * 100));
        }
    }

    private boolean databaseHealthCheck() {
        try {
            dataSource.getConnection().close();
            return true;
        } catch (Exception e) {
            log.error("数据库健康检查失败", e);
            return false;
        }
    }

    private boolean redisHealthCheck() {
        try {
            redisTemplate.execute((RedisCallback<String>) connection -> {
                connection.ping();
                return "PONG";
            });
            return true;
        } catch (Exception e) {
            log.error("Redis健康检查失败", e);
            return false;
        }
    }
}
```

#### 性能阈值配置
```java
@Component
@ConfigurationProperties(prefix = "performance.threshold")
@Data
public class PerformanceThreshold {

    // API响应时间阈值(毫秒)
    public static final int API_RESPONSE_TIME_MAX = 500;
    public static final int QUERY_API_RESPONSE_TIME_MAX = 200;

    // 并发支持要求
    public static final int MIN_CONCURRENT_USERS = 1000;
    public static final int TARGET_CONCURRENT_USERS = 2000;

    // 数据库查询性能要求
    public static final int DB_QUERY_MAX_TIME = 100;
    public static final int DB_TRANSACTION_MAX_TIME = 500;

    // 业务指标阈值
    public static final double APPOINTMENT_SUCCESS_RATE_MIN = 0.95;
    public static final double PAYMENT_SUCCESS_RATE_MIN = 0.98;
    public static final double SYSTEM_ERROR_RATE_MAX = 0.01;

    // 资源使用率阈值
    public static final double DB_CONNECTION_USAGE_MAX = 0.8;
    public static final double MEMORY_USAGE_MAX = 0.85;
    public static final double CPU_USAGE_MAX = 0.8;
}
```

#### 自动化性能测试
```java
@Component
@Slf4j
public class AutomatedPerformanceTest {

    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void runDailyPerformanceTest() {
        log.info("开始执行自动化性能测试");

        try {
            // 测试核心API性能
            testCoreApiPerformance();

            // 测试数据库查询性能
            testDatabasePerformance();

            // 测试并发处理能力
            testConcurrencyPerformance();

            // 生成性能测试报告
            generatePerformanceReport();

        } catch (Exception e) {
            log.error("自动化性能测试执行失败", e);
            alertService.sendAlert(AlertType.SYSTEM, "自动化性能测试执行失败: " + e.getMessage());
        }
    }

    private void testCoreApiPerformance() {
        List<String> coreApis = Arrays.asList(
            "/api/v1/appointments",
            "/api/v1/customers",
            "/api/v1/orders",
            "/api/v1/payments"
        );

        for (String api : coreApis) {
            long startTime = System.currentTimeMillis();

            // 模拟API调用
            ResponseEntity<String> response = restTemplate.getForEntity(api, String.class);

            long responseTime = System.currentTimeMillis() - startTime;

            // 记录性能指标
            meterRegistry.timer("performance.test.api.response.time",
                "endpoint", api
            ).record(responseTime, TimeUnit.MILLISECONDS);

            // 检查是否超过阈值
            if (responseTime > PerformanceThreshold.API_RESPONSE_TIME_MAX) {
                alertService.sendAlert(AlertType.PERFORMANCE,
                    String.format("API %s 响应时间超标: %dms", api, responseTime));
            }
        }
    }

    private void testDatabasePerformance() {
        // 测试常用查询的性能
        Map<String, String> testQueries = Map.of(
            "customer_query", "SELECT * FROM customers WHERE id = ?",
            "appointment_query", "SELECT * FROM appointments WHERE customer_id = ? AND appointment_time > ?",
            "order_query", "SELECT * FROM orders WHERE customer_id = ? ORDER BY created_time DESC LIMIT 10"
        );

        for (Map.Entry<String, String> entry : testQueries.entrySet()) {
            String queryName = entry.getKey();
            String sql = entry.getValue();

            long startTime = System.currentTimeMillis();

            // 执行查询
            jdbcTemplate.queryForList(sql, getSampleParameters(queryName));

            long queryTime = System.currentTimeMillis() - startTime;

            // 记录查询性能
            meterRegistry.timer("performance.test.db.query.time",
                "query", queryName
            ).record(queryTime, TimeUnit.MILLISECONDS);

            // 检查是否超过阈值
            if (queryTime > PerformanceThreshold.DB_QUERY_MAX_TIME) {
                alertService.sendAlert(AlertType.PERFORMANCE,
                    String.format("数据库查询 %s 响应时间超标: %dms", queryName, queryTime));
            }
        }
    }
}
```

---

## 6. 部署和运维规范

### 6.1 容器化部署 【必须】

#### Dockerfile规范
```dockerfile
# 多阶段构建
FROM maven:3.8.4-openjdk-11 AS builder

WORKDIR /app
COPY pom.xml .
COPY src ./src

# 构建应用
RUN mvn clean package -DskipTests

# 运行时镜像
FROM openjdk:11-jre-slim

# 创建非root用户
RUN groupadd -r massage && useradd -r -g massage massage

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制构建产物
COPY --from=builder /app/target/massage-service.jar app.jar

# 设置文件权限
RUN chown -R massage:massage /app
USER massage

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# JVM参数优化
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+PrintGCDetails"

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

#### Docker Compose配置
```yaml
version: '3.8'

services:
  massage-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=**********************************
      - SPRING_REDIS_HOST=redis
      - NACOS_SERVER=nacos:8848
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - massage-network
    restart: unless-stopped
    
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root123
      - MYSQL_DATABASE=massage_db
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - massage-network
    restart: unless-stopped
    
  redis:
    image: redis:6.2-alpine
    networks:
      - massage-network
    restart: unless-stopped
    
  nacos:
    image: nacos/nacos-server:v2.1.0
    environment:
      - MODE=standalone
    ports:
      - "8848:8848"
    networks:
      - massage-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  massage-network:
    driver: bridge
```

### 6.3 数据同步和一致性 【必须】

#### 分布式事务处理
```java
@Service
@Slf4j
public class OrderService {

    @GlobalTransactional // Seata分布式事务
    public OrderResult createOrder(OrderRequest request) {
        try {
            log.info("开始创建订单，客户ID: {}", request.getCustomerId());

            // 1. 创建订单
            Order order = orderRepository.save(buildOrder(request));
            log.info("订单创建成功，订单ID: {}", order.getId());

            // 2. 扣减库存 (调用库存服务)
            InventoryDeductionResult inventoryResult = inventoryService.deductStock(request.getItems());
            if (!inventoryResult.isSuccess()) {
                throw new BusinessException("库存扣减失败: " + inventoryResult.getMessage());
            }
            log.info("库存扣减成功");

            // 3. 扣减余额 (调用支付服务)
            PaymentResult paymentResult = paymentService.deductBalance(
                request.getCustomerId(), order.getAmount());
            if (!paymentResult.isSuccess()) {
                throw new BusinessException("余额扣减失败: " + paymentResult.getMessage());
            }
            log.info("余额扣减成功");

            // 4. 更新会员积分
            memberService.addPoints(request.getCustomerId(), calculatePoints(order.getAmount()));
            log.info("积分增加成功");

            // 5. 发送通知
            notificationService.sendOrderConfirmation(order);

            log.info("订单创建完成，订单ID: {}", order.getId());
            return OrderResult.success(order);

        } catch (Exception e) {
            // 分布式事务自动回滚
            log.error("订单创建失败，事务回滚", e);
            throw new OrderCreationException("订单创建失败: " + e.getMessage());
        }
    }
}
```

#### 数据最终一致性
```java
@Component
@Slf4j
public class DataSyncEventHandler {

    @RabbitListener(queues = "customer.update.queue")
    public void handleCustomerUpdate(CustomerUpdateEvent event) {
        try {
            log.info("处理客户更新事件，客户ID: {}", event.getCustomerId());

            // 同步到数据仓库
            dataWarehouseService.syncCustomerData(event.getCustomerId());

            // 更新搜索索引
            searchService.updateCustomerIndex(event.getCustomerId());

            // 更新缓存
            cacheService.evictCustomerCache(event.getCustomerId());

            // 同步到CRM系统
            crmService.syncCustomerData(event.getCustomerId());

            log.info("客户数据同步完成，客户ID: {}", event.getCustomerId());

        } catch (Exception e) {
            log.error("客户数据同步失败，客户ID: {}", event.getCustomerId(), e);

            // 失败重试机制
            retryTemplate.execute(context -> {
                handleCustomerUpdate(event);
                return null;
            });
        }
    }
}
```

---

## 7. 特殊场景处理指南

### 7.1 离线模式处理 【必须】

#### 离线数据存储
```java
@Service
public class AppointmentService {
    
    private final AppointmentRepository appointmentRepository;
    private final AppointmentMapper appointmentMapper;
    private final NotificationService notificationService;
    private final ApplicationEventPublisher applicationEventPublisher;
    
    private final List<AppointmentOperation> pendingOperations = new ArrayList<>();
    private boolean syncInProgress = false;
    
    public AppointmentService(AppointmentRepository appointmentRepository,
                              AppointmentMapper appointmentMapper,
                              NotificationService notificationService,
                              ApplicationEventPublisher applicationEventPublisher) {
        this.appointmentRepository = appointmentRepository;
        this.appointmentMapper = appointmentMapper;
        this.notificationService = notificationService;
        this.applicationEventPublisher = applicationEventPublisher;
        
        this.loadPendingOperations();
    }
    
    public AppointmentResult createAppointment(CreateAppointmentRequest request) {
        if (isOnline) {
            try {
                return createAppointmentOnline(request);
            } catch (Exception e) {
                saveOfflineOperation(request, "CREATE_APPOINTMENT");
                throw new OfflineException("网络异常，数据已保存到本地，将在网络恢复后自动同步");
            }
        } else {
            saveOfflineOperation(request, "CREATE_APPOINTMENT");
            return AppointmentResult.success(null, true);
        }
    }
    
    private AppointmentResult createAppointmentOnline(CreateAppointmentRequest request) {
        // 业务逻辑
    }
    
    private void saveOfflineOperation(CreateAppointmentRequest request, String operationType) {
        AppointmentOperation operation = new AppointmentOperation();
        operation.setId(UUID.randomUUID().toString());
        operation.setType(operationType);
        operation.setData(appointmentMapper.toEntity(request));
        operation.setTimestamp(LocalDateTime.now());
        operation.setRetryCount(0);
        
        pendingOperations.add(operation);
        savePendingOperations();
        
        showOfflineNotification();
    }
    
    public void syncPendingOperations() {
        if (syncInProgress || pendingOperations.isEmpty()) {
            return;
        }
        
        syncInProgress = true;
        
        try {
            for (AppointmentOperation operation : new ArrayList<>(pendingOperations)) {
                try {
                    switch (operation.getType()) {
                        case "CREATE_APPOINTMENT":
                            createAppointmentOnline(appointmentMapper.toDTO(operation.getData()));
                            break;
                        // 其他操作类型
                    }
                    pendingOperations.remove(operation);
                } catch (Exception e) {
                    operation.setRetryCount(operation.getRetryCount() + 1);
                    if (operation.getRetryCount() >= 3) {
                        handleSyncFailure(operation, e);
                        pendingOperations.remove(operation);
                    }
                }
            }
            savePendingOperations();
            
            if (pendingOperations.isEmpty()) {
                showSyncSuccessNotification();
            }
        } finally {
            syncInProgress = false;
        }
    }
    
    private void handleSyncFailure(AppointmentOperation operation, Exception e) {
        // 处理同步失败的情况
    }
    
    private void showOfflineNotification() {
        // 显示离线提示
    }
    
    private void showSyncSuccessNotification() {
        // 显示同步成功提示
    }
    
    private void loadPendingOperations() {
        // 从持久化存储中加载待同步操作
    }
    
    private void savePendingOperations() {
        // 将待同步操作保存到持久化存储
    }
}
```

### 7.2 离线数据同步机制 【必须】

#### 离线数据存储与同步
```java
class OfflineDataSync {
    private List<Operation> pendingOperations = new ArrayList<>();
    private boolean syncInProgress = false;

    public OfflineDataSync() {
        this.loadPendingOperations();
    }

    public void saveAppointment(AppointmentData appointmentData) {
        if (this.isOnline) {
            try {
                return api.createAppointment(appointmentData);
            } catch (error) {
                // 网络错误时转为离线存储
                this.saveOffline('CREATE_APPOINTMENT', appointmentData);
                throw new OfflineError('网络异常，数据已保存到本地，将在网络恢复后自动同步');
            }
        } else {
            // 离线存储
            this.saveOffline('CREATE_APPOINTMENT', appointmentData);
            return { success: true, offline: true };
        }
    }

    saveOffline(type, data) {
        const operation = {
            id: this.generateId(),
            type: type,
            data: data,
            timestamp: Date.now(),
            retryCount: 0
        };
        
        this.pendingOperations.push(operation);
        this.savePendingOperations();
        
        // 显示离线提示
        this.showOfflineNotification();
    }

    async syncPendingOperations() {
        if (this.syncInProgress || this.pendingOperations.length === 0) {
            return;
        }
        
        this.syncInProgress = true;
        
        try {
            const operations = [...this.pendingOperations];
            
            for (let i = 0; i < operations.length; i++) {
                const operation = operations[i];
                
                try {
                    await this.executeOperation(operation);
                    
                    // 同步成功，从待同步列表中移除
                    this.pendingOperations = this.pendingOperations.filter(op => op.id !== operation.id);
                    
                } catch (error) {
                    operation.retryCount++;
                    
                    // 重试次数超限，标记为失败
                    if (operation.retryCount >= 3) {
                        this.handleSyncFailure(operation, error);
                        this.pendingOperations = this.pendingOperations.filter(op => op.id !== operation.id);
                    }
                }
            }
            
            this.savePendingOperations();
            
            if (this.pendingOperations.length === 0) {
                this.showSyncSuccessNotification();
            }
            
        } finally {
            this.syncInProgress = false;
        }
    }

    async executeOperation(operation) {
        switch (operation.type) {
            case 'CREATE_APPOINTMENT':
                return await api.createAppointment(operation.data);
            case 'UPDATE_APPOINTMENT':
                return await api.updateAppointment(operation.data.id, operation.data);
            case 'CANCEL_APPOINTMENT':
                return await api.cancelAppointment(operation.data.id);
            default:
                throw new Error(`未知的操作类型: ${operation.type}`);
        }
    }

    loadPendingOperations() {
        const stored = localStorage.getItem('pendingOperations');
        if (stored) {
            this.pendingOperations = JSON.parse(stored);
        }
    }

    savePendingOperations() {
        localStorage.setItem('pendingOperations', JSON.stringify(this.pendingOperations));
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}
```

### 7.3 离线数据存储与同步机制 【必须】

#### 离线数据存储与同步
```java
class OfflineDataSync {
    private List<Operation> pendingOperations = new ArrayList<>();
    private boolean syncInProgress = false;

    public OfflineDataSync() {
        this.loadPendingOperations();
    }

    public void saveAppointment(AppointmentData appointmentData) {
        if (this.isOnline) {
            try {
                return api.createAppointment(appointmentData);
            } catch (error) {
                // 网络错误时转为离线存储
                this.saveOffline('CREATE_APPOINTMENT', appointmentData);
                throw new OfflineError('网络异常，数据已保存到本地，将在网络恢复后自动同步');
            }
        } else {
            // 离线存储
            this.saveOffline('CREATE_APPOINTMENT', appointmentData);
            return { success: true, offline: true };
        }
    }

    saveOffline(type, data) {
        const operation = {
            id: this.generateId(),
            type: type,
            data: data,
            timestamp: Date.now(),
            retryCount: 0
        };
        
        this.pendingOperations.push(operation);
        this.savePendingOperations();
        
        // 显示离线提示
        this.showOfflineNotification();
    }

    async syncPendingOperations() {
        if (this.syncInProgress || this.pendingOperations.length === 0) {
            return;
        }
        
        this.syncInProgress = true;
        
        try {
            const operations = [...this.pendingOperations];
            
            for (let i = 0; i < operations.length; i++) {
                const operation = operations[i];
                
                try {
                    await this.executeOperation(operation);
                    
                    // 同步成功，从待同步列表中移除
                    this.pendingOperations = this.pendingOperations.filter(op => op.id !== operation.id);
                    
                } catch (error) {
                    operation.retryCount++;
                    
                    // 重试次数超限，标记为失败
                    if (operation.retryCount >= 3) {
                        this.handleSyncFailure(operation, error);
                        this.pendingOperations = this.pendingOperations.filter(op => op.id !== operation.id);
                    }
                }
            }
            
            this.savePendingOperations();
            
            if (this.pendingOperations.length === 0) {
                this.showSyncSuccessNotification();
            }
            
        } finally {
            this.syncInProgress = false;
        }
    }

    async executeOperation(operation) {
        switch (operation.type) {
            case 'CREATE_APPOINTMENT':
                return await api.createAppointment(operation.data);
            case 'UPDATE_APPOINTMENT':
                return await api.updateAppointment(operation.data.id, operation.data);
            case 'CANCEL_APPOINTMENT':
                return await api.cancelAppointment(operation.data.id);
            default:
                throw new Error(`未知的操作类型: ${operation.type}`);
        }
    }

    loadPendingOperations() {
        const stored = localStorage.getItem('pendingOperations');
        if (stored) {
            this.pendingOperations = JSON.parse(stored);
        }
    }

    savePendingOperations() {
        localStorage.setItem('pendingOperations', JSON.stringify(this.pendingOperations));
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}
```

### 7.4 移动端特殊处理 【必须】

#### 移动端离线数据管理
```javascript
// 前端离线数据同步增强版
class MobileOfflineDataManager {
    constructor() {
        this.pendingOperations = [];
        this.isOnline = navigator.onLine;
        this.syncInProgress = false;
        this.maxRetryCount = 3;
        this.retryDelay = 5000; // 5秒

        // 监听网络状态变化
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.syncPendingOperations();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showOfflineMode();
        });

        // 页面加载时检查待同步数据
        this.loadPendingOperations();
        if (this.isOnline && this.pendingOperations.length > 0) {
            this.syncPendingOperations();
        }
    }

    async saveAppointment(appointmentData) {
        if (this.isOnline) {
            try {
                const result = await api.createAppointment(appointmentData);
                this.showSuccessMessage('预约创建成功');
                return result;
            } catch (error) {
                // 网络错误时转为离线存储
                this.saveOfflineOperation('CREATE_APPOINTMENT', appointmentData);
                this.showOfflineMessage('网络异常，预约已保存到本地，将在网络恢复后自动同步');
                return { success: true, offline: true };
            }
        } else {
            // 离线存储
            this.saveOfflineOperation('CREATE_APPOINTMENT', appointmentData);
            this.showOfflineMessage('当前离线状态，预约已保存到本地');
            return { success: true, offline: true };
        }
    }

    saveOfflineOperation(type, data) {
        const operation = {
            id: this.generateId(),
            type: type,
            data: data,
            timestamp: Date.now(),
            retryCount: 0,
            priority: this.getOperationPriority(type)
        };

        this.pendingOperations.push(operation);
        this.savePendingOperations();

        // 更新离线状态指示器
        this.updateOfflineIndicator();
    }

    async syncPendingOperations() {
        if (this.syncInProgress || this.pendingOperations.length === 0 || !this.isOnline) {
            return;
        }

        this.syncInProgress = true;
        this.showSyncProgress();

        try {
            // 按优先级排序
            const sortedOperations = [...this.pendingOperations].sort((a, b) => b.priority - a.priority);

            for (let i = 0; i < sortedOperations.length; i++) {
                const operation = sortedOperations[i];

                try {
                    await this.executeOperation(operation);

                    // 同步成功，从待同步列表中移除
                    this.pendingOperations = this.pendingOperations.filter(op => op.id !== operation.id);

                    this.updateSyncProgress(i + 1, sortedOperations.length);

                } catch (error) {
                    operation.retryCount++;
                    operation.lastError = error.message;

                    // 重试次数超限，标记为失败
                    if (operation.retryCount >= this.maxRetryCount) {
                        this.handleSyncFailure(operation, error);
                        this.pendingOperations = this.pendingOperations.filter(op => op.id !== operation.id);
                    }
                }
            }

            this.savePendingOperations();

            if (this.pendingOperations.length === 0) {
                this.showSyncSuccessMessage();
            }

        } finally {
            this.syncInProgress = false;
            this.hideSyncProgress();
            this.updateOfflineIndicator();
        }
    }

    getOperationPriority(type) {
        const priorities = {
            'CREATE_APPOINTMENT': 10,
            'CANCEL_APPOINTMENT': 9,
            'UPDATE_APPOINTMENT': 8,
            'CREATE_ORDER': 7,
            'UPDATE_CUSTOMER': 5,
            'CREATE_FEEDBACK': 3
        };
        return priorities[type] || 1;
    }

    showOfflineMode() {
        // 显示离线模式UI
        const offlineBar = document.createElement('div');
        offlineBar.id = 'offline-bar';
        offlineBar.className = 'offline-notification';
        offlineBar.innerHTML = `
            <div class="offline-content">
                <i class="icon-offline"></i>
                <span>当前处于离线模式，数据将在网络恢复后自动同步</span>
                <span class="pending-count">${this.pendingOperations.length}</span>
            </div>
        `;
        document.body.appendChild(offlineBar);
    }

    updateOfflineIndicator() {
        const indicator = document.getElementById('offline-indicator');
        if (indicator) {
            if (this.pendingOperations.length > 0) {
                indicator.textContent = `待同步: ${this.pendingOperations.length}`;
                indicator.style.display = 'block';
            } else {
                indicator.style.display = 'none';
            }
        }
    }
}
```

#### 移动端性能优化
```javascript
// 移动端性能优化管理器
class MobilePerformanceManager {
    constructor() {
        this.imageCache = new Map();
        this.dataCache = new Map();
        this.cacheExpiry = 30 * 60 * 1000; // 30分钟

        // 预加载关键资源
        this.preloadCriticalResources();

        // 监听内存警告
        if ('memory' in performance) {
            this.monitorMemoryUsage();
        }
    }

    // 图片懒加载和缓存
    async loadImage(url, placeholder = '/images/placeholder.jpg') {
        if (this.imageCache.has(url)) {
            const cached = this.imageCache.get(url);
            if (Date.now() - cached.timestamp < this.cacheExpiry) {
                return cached.data;
            }
        }

        try {
            const response = await fetch(url);
            const blob = await response.blob();
            const imageUrl = URL.createObjectURL(blob);

            this.imageCache.set(url, {
                data: imageUrl,
                timestamp: Date.now()
            });

            return imageUrl;
        } catch (error) {
            console.warn('图片加载失败，使用占位图:', error);
            return placeholder;
        }
    }

    // 数据预加载
    async preloadData(endpoints) {
        const preloadPromises = endpoints.map(async endpoint => {
            try {
                const response = await fetch(endpoint);
                const data = await response.json();

                this.dataCache.set(endpoint, {
                    data: data,
                    timestamp: Date.now()
                });

                return { endpoint, success: true };
            } catch (error) {
                console.warn(`预加载失败: ${endpoint}`, error);
                return { endpoint, success: false, error };
            }
        });

        const results = await Promise.allSettled(preloadPromises);
        console.log('数据预加载完成:', results);
    }

    // 内存使用监控
    monitorMemoryUsage() {
        setInterval(() => {
            const memInfo = performance.memory;
            const usageRatio = memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit;

            if (usageRatio > 0.8) {
                console.warn('内存使用率过高，开始清理缓存');
                this.clearExpiredCache();
            }
        }, 60000); // 每分钟检查一次
    }

    clearExpiredCache() {
        const now = Date.now();

        // 清理过期的图片缓存
        for (const [key, value] of this.imageCache.entries()) {
            if (now - value.timestamp > this.cacheExpiry) {
                URL.revokeObjectURL(value.data);
                this.imageCache.delete(key);
            }
        }

        // 清理过期的数据缓存
        for (const [key, value] of this.dataCache.entries()) {
            if (now - value.timestamp > this.cacheExpiry) {
                this.dataCache.delete(key);
            }
        }

        console.log('缓存清理完成');
    }
}
```

### 7.5 高并发预约处理 【必须】

#### 分布式锁实现
```java
@Service
@Slf4j
public class DistributedAppointmentService {
    
    private final RedissonClient redissonClient;
    private final AppointmentRepository appointmentRepository;
    
    public AppointmentResult createAppointmentWithLock(CreateAppointmentRequest request) {
        // 构造锁的key：技师ID + 预约时间
        String lockKey = String.format("appointment:lock:%d:%s", 
            request.getTechnicianId(), 
            request.getAppointmentTime().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm")));
            
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            // 尝试获取锁，最多等待3秒，锁定10秒后自动释放
            boolean acquired = lock.tryLock(3, 10, TimeUnit.SECONDS);
            
            if (!acquired) {
                log.warn("获取预约锁失败，可能存在并发冲突，技师ID: {}, 时间: {}", 
                    request.getTechnicianId(), request.getAppointmentTime());


---

## 9. 开发阶段检查清单

### 9.1 需求分析阶段 ✅

- [ ] 业务流程梳理完整
- [ ] 核心实体识别清晰
- [ ] 微服务边界划分合理
- [ ] 数据隔离策略明确
- [ ] 性能要求量化具体
- [ ] 安全要求详细定义

### 9.2 设计阶段 ✅

#### 架构设计
- [ ] 微服务拆分遵循单一职责原则
- [ ] 服务间通信方式确定（同步/异步）
- [ ] 数据一致性策略制定
- [ ] 容错和降级机制设计

#### 数据库设计
- [ ] 表结构包含租户隔离字段
- [ ] 索引设计考虑查询性能
- [ ] 数据分类和保留策略明确
- [ ] 敏感数据加密方案确定

#### API设计
- [ ] RESTful风格一致
- [ ] 统一响应格式定义
- [ ] 错误码体系完整
- [ ] 版本控制策略制定

### 9.3 编码阶段 ✅

#### 代码质量
- [ ] 命名规范统一
- [ ] 项目结构清晰
- [ ] 注释完整准确
- [ ] 异常处理完善

#### 业务逻辑
- [ ] 核心业务流程实现正确
- [ ] 参数验证完整
- [ ] 业务规则校验严格
- [ ] 事务边界合理

#### 安全实现
- [ ] 敏感数据加密存储
- [ ] 权限控制严格
- [ ] 审计日志完整
- [ ] 输入验证充分

#### 性能优化
- [ ] 数据库查询优化
- [ ] 缓存策略合理
- [ ] 并发处理正确
- [ ] 资源使用高效

### 9.4 测试阶段 ✅

#### 单元测试
- [ ] 测试覆盖率 ≥ 80%
- [ ] 核心业务逻辑全覆盖
- [ ] 边界条件测试充分
- [ ] 异常场景测试完整

#### 集成测试
- [ ] 服务间接口测试
- [ ] 数据库集成测试
- [ ] 外部依赖测试
- [ ] 端到端流程测试

#### 性能测试
- [ ] API响应时间 < 500ms
- [ ] 并发用户数 ≥ 1000
- [ ] 数据库查询性能达标
- [ ] 内存使用合理

#### 安全测试
- [ ] 权限控制验证
- [ ] 数据泄露检查
- [ ] SQL注入防护
- [ ] XSS攻击防护

### 9.5 部署阶段 ✅

#### 环境配置
- [ ] 容器化配置正确
- [ ] 环境变量管理规范
- [ ] 配置中心集成
- [ ] 服务发现配置

#### 监控告警
- [ ] 健康检查端点
- [ ] 关键指标监控
- [ ] 日志收集配置
- [ ] 告警规则设置

#### 数据备份
- [ ] 数据库备份策略
- [ ] 配置文件备份
- [ ] 灾难恢复预案
- [ ] 数据迁移方案

---

## 10. 故障排查指南

### 10.1 常见故障类型

#### 服务启动失败
**症状**：服务无法启动或启动后立即退出
**排查步骤**：
1. 检查日志文件中的错误信息
2. 验证配置文件格式和内容
3. 检查依赖服务是否可用
4. 验证端口是否被占用
5. 检查JVM内存配置

**解决方案示例**：
```bash
# 1. 查看服务日志
docker logs massage-service

# 2. 检查端口占用
netstat -tulpn | grep 8080

# 3. 验证配置文件
java -jar massage-service.jar --spring.config.location=application.yml --debug

# 4. 检查依赖服务
curl http://mysql:3306
curl http://redis:6379
```

#### 数据库连接异常
**症状**：数据库操作失败，连接超时
**排查步骤**：
1. 检查数据库服务状态
2. 验证连接配置
3. 检查网络连通性
4. 查看连接池状态
5. 检查数据库用户权限

**解决方案示例**：
```java
// 数据库健康检查
@Component
public class DatabaseHealthChecker {
    
    @Autowired
    private DataSource dataSource;
    
    public boolean checkDatabaseHealth() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5);
        } catch (SQLException e) {
            log.error("数据库连接检查失败", e);
            return false;
        }
    }
    
    public void printConnectionPoolStatus() {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikari = (HikariDataSource) dataSource;
            log.info("连接池状态 - 活跃连接: {}, 空闲连接: {}, 总连接: {}", 
                hikari.getHikariPoolMXBean().getActiveConnections(),
                hikari.getHikariPoolMXBean().getIdleConnections(),
                hikari.getHikariPoolMXBean().getTotalConnections());
        }
    }
}
```

#### 分布式事务失败
**症状**：部分操作成功，部分失败，数据不一致
**排查步骤**：
1. 检查Seata服务状态
2. 查看全局事务日志
3. 验证各服务事务配置
4. 检查网络连接稳定性
5. 分析业务异常原因

**解决方案示例**：
```java
// 事务状态监控
@Component
@Slf4j
public class TransactionMonitor {
    
    @EventListener
    public void handleGlobalTransactionEvent(GlobalTransactionEvent event) {
        log.info("全局事务事件 - XID: {}, 状态: {}, 耗时: {}ms", 
            event.getXid(), event.getStatus(), event.getDuration());
            
        if (event.getStatus() == GlobalStatus.TimeoutRollbacked) {
            alertService.sendAlert("分布式事务超时回滚", event.getXid());
        } else if (event.getStatus() == GlobalStatus.RollbackFailed) {
            alertService.sendAlert("分布式事务回滚失败", event.getXid());
        }
    }
}
```


## 8. 总结

本文档提供了按摩推拿连锁管理系统开发的完整指导，涵盖了从架构设计到故障排查的各个方面。AI代理在使用本指南时应：

1. **严格遵循架构约束**：确保微服务边界清晰，数据隔离完整
2. **重视数据安全**：敏感数据加密，权限控制严格
3. **关注性能优化**：响应时间、并发处理、资源使用
4. **完善测试覆盖**：单元测试、集成测试、性能测试
5. **建立监控体系**：日志、指标、告警、健康检查

通过遵循这些规则和最佳实践，可以构建出高质量、高可用、高安全的按摩推拿连锁管理系统。

---

## 8. AI代理使用指南

### 8.1 开发流程指导

#### 需求分析阶段
1. **理解业务场景**：深入理解按摩推拿行业的业务特点
2. **识别核心实体**：客户、技师、预约、订单、库存等
3. **梳理业务流程**：预约流程、收银流程、分红流程等
4. **确定技术边界**：微服务划分、数据隔离策略

#### 设计阶段
1. **架构设计**：遵循微服务架构原则
2. **数据库设计**：考虑多租户隔离和性能优化
3. **API设计**：RESTful风格，统一响应格式
4. **安全设计**：数据加密、权限控制、审计日志

#### 编码阶段
1. **代码规范**：严格遵循命名规范和项目结构
2. **业务逻辑**：实现核心业务功能，注重异常处理
3. **数据安全**：敏感数据加密存储和传输
4. **性能优化**：缓存策略、数据库优化、并发处理

#### 测试阶段
1. **单元测试**：覆盖率达到80%以上
2. **集成测试**：验证服务间交互
3. **性能测试**：确保满足响应时间要求
4. **安全测试**：验证权限控制和数据保护

### 8.2 代码生成模板

#### Controller模板
```java
@RestController
@RequestMapping("/api/v1/{resource}")
@Slf4j
@Validated
public class {Resource}Controller {

    private final {Resource}Service {resource}Service;

    @GetMapping
    @PreAuthorize("hasPermission('{resource}', 'READ')")
    public ResponseEntity<ApiResponse<PageResult<{Resource}DTO>>> get{Resource}s(
            @Valid {Resource}QueryRequest request) {

        log.info("查询{resource}列表，参数: {}", request);

        PageResult<{Resource}DTO> result = {resource}Service.get{Resource}s(request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasPermission(#id, '{resource}', 'READ')")
    public ResponseEntity<ApiResponse<{Resource}DTO>> get{Resource}(@PathVariable Long id) {

        log.info("查询{resource}详情，ID: {}", id);

        {Resource}DTO result = {resource}Service.get{Resource}ById(id);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @PostMapping
    @PreAuthorize("hasPermission('{resource}', 'CREATE')")
    public ResponseEntity<ApiResponse<{Resource}DTO>> create{Resource}(
            @Valid @RequestBody Create{Resource}Request request) {

        log.info("创建{resource}，参数: {}", request);

        {Resource}DTO result = {resource}Service.create{Resource}(request);
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(ApiResponse.success(result));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasPermission(#id, '{resource}', 'UPDATE')")
    public ResponseEntity<ApiResponse<{Resource}DTO>> update{Resource}(
            @PathVariable Long id,
            @Valid @RequestBody Update{Resource}Request request) {

        log.info("更新{resource}，ID: {}, 参数: {}", id, request);

        {Resource}DTO result = {resource}Service.update{Resource}(id, request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasPermission(#id, '{resource}', 'DELETE')")
    public ResponseEntity<ApiResponse<Void>> delete{Resource}(@PathVariable Long id) {

        log.info("删除{resource}，ID: {}", id);

        {resource}Service.delete{Resource}(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }
}
```

#### Service模板
```java
@Service
@Transactional
@Slf4j
public class {Resource}Service {

    private final {Resource}Repository {resource}Repository;
    private final {Resource}Mapper {resource}Mapper;

    @Transactional(readOnly = true)
    public PageResult<{Resource}DTO> get{Resource}s({Resource}QueryRequest request) {
        log.debug("查询{resource}列表，参数: {}", request);

        // 构建查询条件
        Page<{Resource}> page = {resource}Repository.findAll(
            buildSpecification(request),
            PageRequest.of(request.getPageNum() - 1, request.getPageSize())
        );

        List<{Resource}DTO> dtos = page.getContent().stream()
            .map({resource}Mapper::toDTO)
            .collect(Collectors.toList());

        return PageResult.<{Resource}DTO>builder()
            .pageNum(request.getPageNum())
            .pageSize(request.getPageSize())
            .total(page.getTotalElements())
            .pages(page.getTotalPages())
            .list(dtos)
            .build();
    }

    @Transactional(readOnly = true)
    public {Resource}DTO get{Resource}ById(Long id) {
        log.debug("查询{resource}详情，ID: {}", id);

        {Resource} entity = {resource}Repository.findById(id)
            .orElseThrow(() -> new NotFoundException("{Resource}不存在，ID: " + id));

        return {resource}Mapper.toDTO(entity);
    }

    public {Resource}DTO create{Resource}(Create{Resource}Request request) {
        log.info("创建{resource}，参数: {}", request);

        // 业务验证
        validate{Resource}Request(request);

        // 构建实体
        {Resource} entity = {resource}Mapper.toEntity(request);
        entity.setTenantId(TenantContext.getCurrentTenantId());

        // 保存实体
        entity = {resource}Repository.save(entity);

        // 发布事件
        applicationEventPublisher.publishEvent(new {Resource}CreatedEvent(entity.getId()));

        log.info("{Resource}创建成功，ID: {}", entity.getId());
        return {resource}Mapper.toDTO(entity);
    }

    public {Resource}DTO update{Resource}(Long id, Update{Resource}Request request) {
        log.info("更新{resource}，ID: {}, 参数: {}", id, request);

        {Resource} entity = {resource}Repository.findById(id)
            .orElseThrow(() -> new NotFoundException("{Resource}不存在，ID: " + id));

        // 业务验证
        validate{Resource}UpdateRequest(entity, request);

        // 更新实体
        {resource}Mapper.updateEntity(entity, request);
        entity = {resource}Repository.save(entity);

        // 发布事件
        applicationEventPublisher.publishEvent(new {Resource}UpdatedEvent(entity.getId()));

        log.info("{Resource}更新成功，ID: {}", entity.getId());
        return {resource}Mapper.toDTO(entity);
    }

    public void delete{Resource}(Long id) {
        log.info("删除{resource}，ID: {}", id);

        {Resource} entity = {resource}Repository.findById(id)
            .orElseThrow(() -> new NotFoundException("{Resource}不存在，ID: " + id));

        // 业务验证
        validateDelete{Resource}(entity);

        // 软删除
        entity.setDeleted(true);
        entity.setDeletedTime(LocalDateTime.now());
        {resource}Repository.save(entity);

        // 发布事件
        applicationEventPublisher.publishEvent(new {Resource}DeletedEvent(entity.getId()));

        log.info("{Resource}删除成功，ID: {}", id);
    }

    private void validate{Resource}Request(Create{Resource}Request request) {
        // 业务验证逻辑
    }

    private Specification<{Resource}> buildSpecification({Resource}QueryRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 租户隔离
            predicates.add(criteriaBuilder.equal(root.get("tenantId"), TenantContext.getCurrentTenantId()));

            // 软删除过滤
            predicates.add(criteriaBuilder.equal(root.get("deleted"), false));

            // 其他查询条件...

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
```

### 8.3 常见问题解决方案

#### 问题1：多租户数据泄露
**现象**：不同租户可以看到其他租户的数据
**原因**：未正确设置租户上下文或查询条件
**解决方案**：
```java
// 1. 确保在Filter中设置租户上下文
@Component
public class TenantFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        String tenantId = extractTenantId((HttpServletRequest) request);
        TenantContext.setCurrentTenantId(Long.valueOf(tenantId));
        try {
            chain.doFilter(request, response);
        } finally {
            TenantContext.clear();
        }
    }
}

// 2. 在Repository查询中添加租户过滤
@Query("SELECT a FROM Appointment a WHERE a.tenantId = :#{T(com.massage.common.TenantContext).getCurrentTenantId()}")
List<Appointment> findAllByCurrentTenant();
```

#### 问题2：分布式事务回滚失败
**现象**：部分服务操作成功，部分失败，数据不一致
**原因**：未正确配置Seata或事务传播机制
**解决方案**：
```java
// 1. 确保Seata配置正确
@GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 30000)
public void businessMethod() {
    // 业务逻辑
}

// 2. 检查服务调用是否传递事务上下文
@FeignClient(name = "inventory-service")
public interface InventoryService {
    @PostMapping("/api/v1/inventory/deduct")
    InventoryResult deductStock(@RequestBody DeductStockRequest request);
}
```

#### 问题3：高并发下预约冲突
**现象**：同一时间段被多个客户预约成功
**原因**：缺少分布式锁或锁粒度不当
**解决方案**：
```java
// 使用Redis分布式锁
@Service
public class AppointmentService {

    public AppointmentResult createAppointment(CreateAppointmentRequest request) {
        String lockKey = "appointment:" + request.getTechnicianId() + ":" + request.getTimeSlot();

        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                // 在锁保护下执行预约逻辑
                return doCreateAppointment(request);
            } else {
                throw new BusinessException("预约冲突，请重试");
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

#### 问题4：性能瓶颈排查
**现象**：API响应时间过长，系统响应缓慢
**原因**：数据库查询慢、缓存失效、线程池满等
**解决方案**：
```java
// 1. 慢查询监控
@Component
public class SlowQueryInterceptor implements Interceptor {
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long start = System.currentTimeMillis();
        Object result = invocation.proceed();
        long duration = System.currentTimeMillis() - start;

        if (duration > 1000) { // 超过1秒的查询
            log.warn("慢查询检测: 耗时{}ms, SQL: {}", duration, getSql(invocation));
        }
        return result;
    }
}

// 2. 缓存优化
@Service
public class CustomerService {

    @Cacheable(value = "customers", key = "#id", unless = "#result == null")
    public CustomerDTO getCustomerById(Long id) {
        return customerRepository.findById(id)
            .map(customerMapper::toDTO)
            .orElse(null);
    }

    @CacheEvict(value = "customers", key = "#id")
    public void updateCustomer(Long id, UpdateCustomerRequest request) {
        // 更新逻辑
    }
}
```

#### 问题5：内存泄漏问题
**现象**：应用运行一段时间后内存持续增长，最终OOM
**原因**：对象未正确释放、缓存无限增长、线程池未关闭等
**解决方案**：
```java
// 1. 定时清理缓存
@Component
public class CacheCleanupScheduler {

    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点清理
    public void cleanupExpiredCache() {
        cacheManager.getCacheNames().forEach(cacheName -> {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache instanceof CaffeineCache) {
                ((CaffeineCache) cache).getNativeCache().cleanUp();
            }
        });
    }
}

// 2. 资源正确释放
@Service
public class FileProcessingService {

    public void processFile(String filePath) {
        try (FileInputStream fis = new FileInputStream(filePath);
             BufferedReader reader = new BufferedReader(new InputStreamReader(fis))) {

            // 处理文件逻辑

        } catch (IOException e) {
            log.error("文件处理失败", e);
        }
        // try-with-resources 自动关闭资源
    }
}
```

---

## 9. 开发阶段检查清单

### 9.1 需求分析阶段 ✅

- [ ] 业务流程梳理完整
- [ ] 核心实体识别清晰
- [ ] 微服务边界划分合理
- [ ] 数据隔离策略明确
- [ ] 性能要求量化具体
- [ ] 安全要求详细定义

### 9.2 设计阶段 ✅

#### 架构设计
- [ ] 微服务拆分遵循单一职责原则
- [ ] 服务间通信方式确定（同步/异步）
- [ ] 数据一致性策略制定
- [ ] 容错和降级机制设计

#### 数据库设计
- [ ] 表结构包含租户隔离字段
- [ ] 索引设计考虑查询性能
- [ ] 数据分类和保留策略明确
- [ ] 敏感数据加密方案确定

#### API设计
- [ ] RESTful风格一致
- [ ] 统一响应格式定义
- [ ] 错误码体系完整
- [ ] 版本控制策略制定

### 9.3 编码阶段 ✅

#### 代码质量
- [ ] 命名规范统一
- [ ] 项目结构清晰
- [ ] 注释完整准确
- [ ] 异常处理完善

#### 业务逻辑
- [ ] 核心业务流程实现正确
- [ ] 参数验证完整
- [ ] 业务规则校验严格
- [ ] 事务边界合理

#### 安全实现
- [ ] 敏感数据加密存储
- [ ] 权限控制严格
- [ ] 审计日志完整
- [ ] 输入验证充分

#### 性能优化
- [ ] 数据库查询优化
- [ ] 缓存策略合理
- [ ] 并发处理正确
- [ ] 资源使用高效

### 9.4 测试阶段 ✅

#### 单元测试
- [ ] 测试覆盖率 ≥ 80%
- [ ] 核心业务逻辑全覆盖
- [ ] 边界条件测试充分
- [ ] 异常场景测试完整

#### 集成测试
- [ ] 服务间接口测试
- [ ] 数据库集成测试
- [ ] 外部依赖测试
- [ ] 端到端流程测试

#### 性能测试
- [ ] API响应时间 < 500ms
- [ ] 并发用户数 ≥ 1000
- [ ] 数据库查询性能达标
- [ ] 内存使用合理

#### 安全测试
- [ ] 权限控制验证
- [ ] 数据泄露检查
- [ ] SQL注入防护
- [ ] XSS攻击防护

### 9.5 部署阶段 ✅

#### 环境配置
- [ ] 容器化配置正确
- [ ] 环境变量管理规范
- [ ] 配置中心集成
- [ ] 服务发现配置

#### 监控告警
- [ ] 健康检查端点
- [ ] 关键指标监控
- [ ] 日志收集配置
- [ ] 告警规则设置

#### 数据备份
- [ ] 数据库备份策略
- [ ] 配置文件备份
- [ ] 灾难恢复预案
- [ ] 数据迁移方案

### 9.6 上线前最终检查 ✅

#### 功能验证
- [ ] 核心业务流程端到端测试
- [ ] 六端权限验证完整
- [ ] 数据隔离验证正确
- [ ] 异常处理验证完善

#### 性能验证
- [ ] 压力测试通过
- [ ] 内存泄漏检查通过
- [ ] 数据库连接池配置合理
- [ ] 缓存命中率达标

#### 安全验证
- [ ] 敏感数据加密验证
- [ ] 权限控制测试通过
- [ ] 审计日志功能正常
- [ ] 安全扫描无高危漏洞

#### 运维准备
- [ ] 监控大盘配置完成
- [ ] 告警通知渠道测试
- [ ] 应急预案文档完整
- [ ] 回滚方案验证可行

---

## 10. 故障排查指南

### 10.1 常见故障类型

#### 服务启动失败
**症状**：服务无法启动或启动后立即退出
**排查步骤**：
1. 检查日志文件中的错误信息
2. 验证配置文件格式和内容
3. 检查依赖服务是否可用
4. 验证端口是否被占用
5. 检查JVM内存配置

**解决方案示例**：
```bash
# 1. 查看服务日志
docker logs massage-service

# 2. 检查端口占用
netstat -tulpn | grep 8080

# 3. 验证配置文件
java -jar massage-service.jar --spring.config.location=application.yml --debug

# 4. 检查依赖服务
curl http://mysql:3306
curl http://redis:6379
```

#### 数据库连接异常
**症状**：数据库操作失败，连接超时
**排查步骤**：
1. 检查数据库服务状态
2. 验证连接配置
3. 检查网络连通性
4. 查看连接池状态
5. 检查数据库用户权限

**解决方案示例**：
```java
// 数据库健康检查
@Component
public class DatabaseHealthChecker {

    @Autowired
    private DataSource dataSource;

    public boolean checkDatabaseHealth() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5);
        } catch (SQLException e) {
            log.error("数据库连接检查失败", e);
            return false;
        }
    }

    public void printConnectionPoolStatus() {
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikari = (HikariDataSource) dataSource;
            log.info("连接池状态 - 活跃连接: {}, 空闲连接: {}, 总连接: {}",
                hikari.getHikariPoolMXBean().getActiveConnections(),
                hikari.getHikariPoolMXBean().getIdleConnections(),
                hikari.getHikariPoolMXBean().getTotalConnections());
        }
    }
}
```

#### 分布式事务失败
**症状**：部分操作成功，部分失败，数据不一致
**排查步骤**：
1. 检查Seata服务状态
2. 查看全局事务日志
3. 验证各服务事务配置
4. 检查网络连接稳定性
5. 分析业务异常原因

**解决方案示例**：
```java
// 事务状态监控
@Component
@Slf4j
public class TransactionMonitor {

    @EventListener
    public void handleGlobalTransactionEvent(GlobalTransactionEvent event) {
        log.info("全局事务事件 - XID: {}, 状态: {}, 耗时: {}ms",
            event.getXid(), event.getStatus(), event.getDuration());

        if (event.getStatus() == GlobalStatus.TimeoutRollbacked) {
            alertService.sendAlert("分布式事务超时回滚", event.getXid());
        } else if (event.getStatus() == GlobalStatus.RollbackFailed) {
            alertService.sendAlert("分布式事务回滚失败", event.getXid());
        }
    }
}
```

### 10.2 性能问题排查

#### API响应慢
**排查步骤**：
1. 分析慢查询日志
2. 检查缓存命中率
3. 查看JVM GC情况
4. 分析线程池状态
5. 检查外部依赖响应时间

**监控工具配置**：
```java
// 性能监控配置
@Configuration
public class PerformanceMonitoringConfig {

    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }

    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config().commonTags("application", "massage-service");
    }
}

// 方法级性能监控
@Service
public class AppointmentService {

    @Timed(name = "appointment.create", description = "预约创建耗时")
    public AppointmentResult createAppointment(CreateAppointmentRequest request) {
        // 业务逻辑
        return result;
    }
}
```

#### 内存泄漏
**排查步骤**：
1. 生成堆转储文件
2. 分析对象引用关系
3. 检查缓存配置
4. 查看线程池状态
5. 分析GC日志

**内存监控示例**：
```java
// 内存使用监控
@Component
@Slf4j
public class MemoryMonitor {

    private final MeterRegistry meterRegistry;

    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void monitorMemoryUsage() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();

        double usedPercent = (double) heapUsage.getUsed() / heapUsage.getMax() * 100;

        Gauge.builder("jvm.memory.heap.used.percent")
            .register(meterRegistry)
            .set(usedPercent);

        if (usedPercent > 80) {
            log.warn("堆内存使用率过高: {}%", String.format("%.2f", usedPercent));

            if (usedPercent > 90) {
                // 触发告警
                alertService.sendAlert("内存使用率告警",
                    String.format("堆内存使用率: %.2f%%", usedPercent));
            }
        }
    }

    @EventListener
    public void handleOutOfMemoryError(OutOfMemoryError error) {
        log.error("内存溢出错误", error);

        // 生成堆转储
        try {
            String dumpPath = "/tmp/heap-dump-" + System.currentTimeMillis() + ".hprof";
            ManagementFactory.getPlatformMXBean(HotSpotDiagnosticMXBean.class)
                .dumpHeap(dumpPath, true);
            log.info("堆转储文件已生成: {}", dumpPath);
        } catch (Exception e) {
            log.error("生成堆转储失败", e);
        }
    }
}
```

### 10.3 应急处理流程

#### 服务不可用
**处理步骤**：
1. 立即启用降级策略
2. 检查服务健康状态
3. 分析错误日志
4. 执行服务重启
5. 验证服务恢复

**降级策略示例**：
```java
// 服务降级配置
@Component
public class ServiceDegradationManager {

    private final CircuitBreakerRegistry circuitBreakerRegistry;

    public AppointmentResult createAppointmentWithFallback(CreateAppointmentRequest request) {
        CircuitBreaker circuitBreaker = circuitBreakerRegistry.circuitBreaker("appointment-service");

        return circuitBreaker.executeSupplier(() -> {
            // 正常业务逻辑
            return appointmentService.createAppointment(request);
        }).recover(throwable -> {
            // 降级逻辑
            log.warn("预约服务降级，原因: {}", throwable.getMessage());
            return AppointmentResult.builder()
                .success(false)
                .message("系统繁忙，请稍后重试")
                .build();
        });
    }
}
```

#### 数据不一致
**处理步骤**：
1. 停止相关业务操作
2. 备份当前数据状态
3. 分析数据差异
4. 执行数据修复
5. 验证数据一致性

**数据修复工具**：
```java
// 数据一致性检查和修复
@Component
@Slf4j
public class DataConsistencyChecker {

    public void checkAndRepairAppointmentData() {
        log.info("开始检查预约数据一致性");

        List<Appointment> appointments = appointmentRepository.findAll();
        int repairCount = 0;

        for (Appointment appointment : appointments) {
            // 检查预约状态与支付状态一致性
            if (appointment.getStatus() == AppointmentStatus.CONFIRMED
                && appointment.getPaymentStatus() != PaymentStatus.PAID) {

                log.warn("发现数据不一致 - 预约ID: {}, 预约状态: {}, 支付状态: {}",
                    appointment.getId(), appointment.getStatus(), appointment.getPaymentStatus());

                // 查询支付记录
                Payment payment = paymentRepository.findByAppointmentId(appointment.getId());
                if (payment != null && payment.getStatus() == PaymentStatus.SUCCESS) {
                    // 修复预约支付状态
                    appointment.setPaymentStatus(PaymentStatus.PAID);
                    appointmentRepository.save(appointment);
                    repairCount++;

                    log.info("已修复预约支付状态 - 预约ID: {}", appointment.getId());
                }
            }
        }

        log.info("数据一致性检查完成，修复记录数: {}", repairCount);
    }
}
```

---

### 10.2 性能问题排查

#### API响应慢
**排查步骤**：
1. 分析慢查询日志
2. 检查缓存命中率
3. 查看JVM GC情况
4. 分析线程池状态
5. 检查外部依赖响应时间

**监控工具配置**：
```java
// 性能监控配置
@Configuration
public class PerformanceMonitoringConfig {
    
    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }
    
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config().commonTags("application", "massage-service");
    }
}

// 方法级性能监控
@Service
public class AppointmentService {
    
    @Timed(name = "appointment.create", description = "预约创建耗时")
    public AppointmentResult createAppointment(CreateAppointmentRequest request) {
        // 业务逻辑
    }
}
```

#### 内存泄露
**排查步骤**：
1. 生成堆转储文件
2. 分析内存使用情况
3. 检查对象引用关系
4. 查找内存泄露点
5. 优化代码实现

**内存监控示例**：
```java
// 内存使用监控
@Component
@Slf4j
public class MemoryMonitor {
    
    @Scheduled(fixedRate = 60000) // 每分钟检查
    public void checkMemoryUsage() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        
        long used = heapUsage.getUsed();
        long max = heapUsage.getMax();
        double usagePercent = (double) used / max * 100;
        
        log.info("堆内存使用情况 - 已使用: {}MB, 最大: {}MB, 使用率: {:.2f}%", 
            used / 1024 / 1024, max / 1024 / 1024, usagePercent);
            
        if (usagePercent > 80) {
            log.warn("内存使用率过高: {:.2f}%", usagePercent);
            alertService.sendAlert(AlertType.MEMORY, "内存使用率过高: " + usagePercent + "%");
        }
    }
}
```

### 10.3 数据一致性问题

#### 数据不一致排查
**排查步骤**：
1. 检查事务边界设置
2. 分析并发操作日志
3. 验证数据同步机制
4. 检查缓存更新策略
5. 分析业务逻辑错误

**数据一致性检查工具**：
```java
// 数据一致性检查
@Service
@Slf4j
public class DataConsistencyChecker {
    
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void checkDataConsistency() {
        log.info("开始执行数据一致性检查");
        
        // 检查订单和支付记录一致性
        checkOrderPaymentConsistency();
        
        // 检查库存和订单一致性
        checkInventoryOrderConsistency();
        
        // 检查会员余额和交易记录一致性
        checkMemberBalanceConsistency();
        
        log.info("数据一致性检查完成");
    }
    
    private void checkOrderPaymentConsistency() {
        List<Order> paidOrders = orderRepository.findByStatus(OrderStatus.PAID);
        
        for (Order order : paidOrders) {
            List<Payment> payments = paymentRepository.findByOrderId(order.getId());
            BigDecimal totalPaid = payments.stream()
                .map(Payment::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
                
            if (totalPaid.compareTo(order.getTotalAmount()) != 0) {
                log.error("订单支付金额不一致 - 订单ID: {}, 订单金额: {}, 支付金额: {}", 
                    order.getId(), order.getTotalAmount(), totalPaid);
                    
                alertService.sendAlert(AlertType.DATA_INCONSISTENCY, 
                    "订单支付金额不一致，订单ID: " + order.getId());
            }
        }
    }
}
```

### 10.4 日志分析工具

#### 结构化日志查询
```bash
# 查询特定时间段的错误日志
grep "ERROR" massage-service.log | grep "2024-07-26 14:"

# 查询特定用户的操作日志
grep "userId:12345" massage-service.log

# 查询API响应时间超过500ms的请求
grep "duration" massage-service.log | awk '$NF > 500'

# 统计错误类型分布
grep "ERROR" massage-service.log | awk '{print $6}' | sort | uniq -c | sort -nr
```

#### 日志聚合分析
```java
// 日志聚合分析
@Component
@Slf4j
public class LogAnalyzer {
    
    private final Map<String, AtomicLong> errorCounts = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> apiCounts = new ConcurrentHashMap<>();
    
    @EventListener
    public void handleErrorEvent(ErrorEvent event) {
        String errorType = event.getException().getClass().getSimpleName();
        errorCounts.computeIfAbsent(errorType, k -> new AtomicLong(0)).incrementAndGet();
    }
    
    @EventListener
    public void handleApiCallEvent(ApiCallEvent event) {
        String apiPath = event.getPath();
        apiCounts.computeIfAbsent(apiPath, k -> new AtomicLong(0)).incrementAndGet();
    }
    
    @Scheduled(fixedRate = 300000) // 每5分钟输出统计
    public void printStatistics() {
        log.info("=== 错误统计 ===");
        errorCounts.forEach((errorType, count) -> 
            log.info("{}: {}", errorType, count.get()));
            
        log.info("=== API调用统计 ===");
        apiCounts.entrySet().stream()
            .sorted(Map.Entry.<String, AtomicLong>comparingByValue(
                (a, b) -> Long.compare(b.get(), a.get())))
            .limit(10)
            .forEach(entry -> 
                log.info("{}: {}", entry.getKey(), entry.getValue().get()));
    }
}
```

---
## 结语

本开发指南为按摩推拿连锁管理系统的AI代理开发提供了全面的技术规范和最佳实践。通过遵循本指南中的架构设计、编码规范、安全要求和性能优化策略，可以构建出高质量、高可用、高安全的企业级应用系统。

### 关键要点总结

1. **架构设计**：采用微服务架构，确保系统的可扩展性和可维护性
2. **数据安全**：实施多层次的数据保护措施，确保客户隐私和业务数据安全
3. **性能优化**：通过缓存、数据库优化、并发控制等手段提升系统性能
4. **质量保证**：建立完善的测试体系和监控机制，确保系统稳定运行
5. **运维支持**：提供详细的故障排查指南和应急处理流程

### 持续改进

技术发展日新月异，本指南也将根据实际项目经验和技术发展趋势持续更新和完善。建议开发团队：

- 定期回顾和更新开发规范
- 收集和分析生产环境问题
- 关注新技术和最佳实践
- 持续优化系统架构和性能

通过不断的学习和实践，我们能够为按摩推拿行业提供更加优质的数字化解决方案。
```
      - "8848:8848"
    networks:
      - massage-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  massage-network:
    driver: bridge
```
