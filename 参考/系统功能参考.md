《按摩推拿连锁管理系统 - 功能规格清单》

版本: 6.0 (行业深度定制版)
核心特性: 门店独立定价、股东分红核算、全流程自动化、业财一体化、AI驱动决策

第一部分：基础管理模块 (总部/店长端)

1.1 门店管理 (ID: FM-01-01)

功能: 创建、编辑、停用门店信息。

字段: 门店ID, 门店名称, 详细地址, 联系电话, 营业时间, 门店状态 (营业中/停用), 电子小票LOGO, GPS坐标。

1.2 [新增] 股东与股权管理 (ID: FM-01-02)
功能: 为多股东、多门店的复杂股权结构提供专业的权益管理与核算工具。
关键点:
- 股东档案: 管理股东基本信息、出资记录、股权比例等。
- 股权结构: 支持配置不同门店、不同项目的复杂股权结构。
- 分红核算: 内置多种分红模型（如按营收比例、按利润比例），自动计算股东分红，并生成详细报表。
- 权益变动: 记录股东的增资、撤资、股权转让等操作，历史可追溯。

1.3 员工档案管理 (ID: FM-01-03)

功能: 管理系统内所有员工的档案、账户和技能。

字段: 员工ID, 姓名, 性别, 照片, 职位 (店长/技师/助理等), 所属门店, 联系电话, 入职日期, 员工状态 (在职/离职), 默认提成方案ID, 技能标签 (可多选), 身份证/健康证/资格证等附件。

1.4 服务与商品库管理 (ID: OP-01-04)

功能: 建立集团统一的“标准项目库”，并授权各门店进行个性化定价和项目启用。

流程:

总部: 在【集团项目库】创建项目，设定【集团标准价】。

店长: 在【本店项目列表】中，可对每个项目进行【独立定价】、【启用/禁用】、【设置本店专属提成方案】。

字段:

集团项目表: 项目ID, 项目名称, 项目类型 (服务/商品), 集团标准价。

门店项目关联表: 关联ID, 项目ID, 门店ID, 本店价格, 是否启用 (布尔值)。

1.5 [新增] 动态提成方案管理 (ID: FM-01-05)

功能: 创建和管理多样化、可灵活配置的员工提成方案，以适应不同项目、活动和员工角色的激励需求。

提成规则配置:

提成模式: 支持按服务实收金额百分比、项目原价百分比、固定金额等多种基数计算。

阶梯提成: 支持设置个人或团队业绩阶梯，业绩越高，提成比例越高。

角色提成: 支持为同一订单中的不同角色（如服务技师、助理、拓客推荐人）设置独立的提成规则和比例。

分类提成: 支持为【服务项目】和【销售商品】设置不同的提成方案。

专项提成: 可创建临时性的【高额提成方案】，并关联到指定的推广项目或营销活动上。

字段: 提成方案ID, 方案名称, 提成规则详情 (JSON格式), 适用范围 (全员/指定职位/指定员工)。

1.6 [新增] 培训与知识库 (ID: FM-01-06)

功能: 建立企业内部的知识共享中心，用于员工培训和标准化执行。

流程: 总部或店长上传标准化资料，员工可在自己终端随时查阅学习。

字段: 知识ID, 标题, 内容分类 (SOP/产品知识/培训视频/公告), 内容正文/视频链接, 发布日期, 已读员工列表。

第二部分：会员运营与营销模块 (全端涉及)

2.1 会员360°视图 (ID: MM-02-01)

功能: 全方位展示单个会员的所有相关信息，形成客户画像。

字段: 手机号, 姓名, 性别, 生日, 会员等级, 储值余额, 消费积分, 优惠券列表, 累计消费金额, 最近到店日期, 常做项目Top3, 常找技师Top3, 客户来源 (如: 张三介绍), 自定义标签 (如发质/过敏史/服务备注)。

2.2 储值与等级体系 (ID: MM-02-02)

功能: 配置会员充值赠送规则和基于消费的等级成长体系。

字段: 充值方案ID, 充值金额, 赠送金额; 等级名称, 升级所需累计消费, 专享折扣率。

2.3 [新增] 营销活动中心 (ID: MM-02-03)

功能: 创建和管理多样化的营销活动与优惠券，用于拉新、促活和提升客单价。

优惠券工具:

类型: 满减券、折扣券、项目体验券、通用现金券。

发放场景: 支持系统自动发放（注册礼/消费赠送/生日礼）、用户在领券中心手动领取、后台精准推送给特定会员标签群组。

营销活动: 支持创建拼团活动、二人同行一人免单、限时折扣活动等，系统在收银时能自动识别活动并进行核销。

字段: 活动ID, 活动名称, 活动类型, 活动规则, 开始/结束时间, 适用门店。

2.4 [新增] 客户转介绍体系 (ID: MM-02-04)

功能: 将“熟客介绍”系统化，通过奖励机制激励老客带来新客，实现社交裂变。

流程:

系统为每位会员生成专属的推荐二维码或分享链接。

老会员分享后，新客通过该链接注册或完成首单消费。

系统自动为老会员发放预设奖励（积分/优惠券/储值金）。

字段: 推荐关系表 (推荐人ID, 被推荐人ID, 绑定时间), 奖励记录表。

2.5 [新增] 营销自动化 (MA) (ID: MM-02-05)

功能: 基于预设规则，自动化地对不同生命周期阶段的会员执行关怀和营销动作。

规则引擎:

流失唤醒: 对超过90天未到店的【沉睡会员】，自动发送召回短信并附赠一张专属优惠券。

消费激励: 当会员储值余额低于50元时，自动推送充值优惠活动信息。

生日关怀: 在会员生日前7天，自动发送生日祝福并赠送一张生日礼券。

项目推广: 对做过“染发”项目但未做过“烫发”的会员，在1个月后自动推送烫发项目的优惠信息。

第三部分：核心运营模块 (全端涉及)

3.1 可视化预约日历 (ID: OP-03-01)

功能: 以日历形式直观展示和管理所有预约，支持顾客自助预约和后台管理。

字段: 预约ID, 会员ID, 技师ID, 关联资源ID (如VIP房/特定仪器), 服务项目ID, 预约开始时间, 预计结束时间, 预约状态 (待到店/已到店/服务中/已完成/已取消/未到店)。

优化建议:

增加预约冲突检测: 同一时间段内，同一技师或同一资源不能被重复预约。

增加预约锁定机制: 用户在选择时间段后，系统自动锁定该时间段15分钟。

3.2 预约到店确认流程 (ID: OP-03-02)

功能: 建立从顾客提醒到员工确认的闭环。

流程: 预约前指定时间，系统自动向【未到店】顾客发送微信提醒。顾客到店后，【员工端】点击【确认到店】，系统停止提醒，状态更新。

3.3 自动排班系统 (ID: OP-03-03)

功能: 自动化为门店技师生成每日服务排班顺序，支持多种轮候规则。

规则: 轮钟模式 (依次循环)、业绩优先模式 (按前一日业绩排序)、指定顺序模式 (店长手动拖拽)。

字段: 排班规则设置 (门店ID, 规则类型), 每日轮候队列 (日期, 门店ID, 技师顺序列表)。

3.4 顾客随访管理 (ID: OP-03-04)

功能: 在顾客消费后，系统化地安排并提醒员工进行客户关怀回访。

流程: 收银时设置随访时间（如3天后）和要点 -> 系统到期提醒员工 -> 员工完成后填写结果。

字段: 随访任务ID, 关联订单ID, 会员ID, 待随访员工ID, 计划随访日期, 初始随访内容, 随访状态 (待执行/已完成), 随访结果反馈。

3.5 多平台评价管理 (ID: OP-03-05)

功能: 集中管理来自美团、大众点评等多个平台的员工评价。

[优化后] 流程: 优先通过API或RPA技术自动同步各平台评价数据。若无接口，则保留店长手动录入作为补充， 录入各平台对应员工的好、中、差评数及截图。系统自动计算综合评分。

字段: 评价ID, 员工ID, 平台名称, 评价日期, 好评数, 中评数, 差评数, 截图存储路径, 原始评价内容。

第四部分：收银与财务模块 (收银/员工/店长/总部端)

4.1 收银台 (POS) (ID: POS-04-01)

功能: 完成服务和商品的结算收款，并记录全面的订单信息。员工端也可发起。

录入字段:

客户: 客户类型 (会员/散客), 手机号 (散客), 顾客称呼 (散客)。

项目: 服务/商品ID, 数量, 服务员工ID。

渠道: 项目来源 (自然到店/美团验券/抖音验券), 核销券码。

归因: 客源渠道 (熟客介绍/小红书等), 服务总人数。

支付: 支持混合支付（现金/微信/支付宝/余额/积分/优惠券），支持电子签名确认。

结算: 支持会员跨店消费后的统一结算与对账。

4.2 动态定价引擎 (ID: POS-04-02)

功能: 自动计算顾客最终应付的准确价格。

优先级顺序: 1. 门店独立价格 -> 2. 集团标准价格 -> 3. 促销活动价 -> 4. 会员等级折扣 -> 5. 原价。

4.3 电子小票系统 (ID: POS-04-03)

功能: 支付成功后，自动生成HTML格式电子小票，并通过微信推送给顾客。

字段: 门店LOGO, 订单号, 消费时间, 项目明细, 折扣金额, 实付金额, 账户余额, 防伪二维码。

4.4 自定义成本管理 (ID: FIN-04-04)

功能: 用于核算门店的运营成本，计算真实利润。

字段: 成本项ID, 门店ID, 成本名称 (房租/水电/物料/人力成本), 成本类型 (固定值/变动值), 金额, 发生周期 (每日/每月/每年)。

成本核算: 支持按门店、项目、时间段等多维度进行成本核算与分摊。

预算管理: 支持成本预算编制与实际发生对比分析。

4.5 [新增] 财务软件集成 (ID: FIN-04-05)
功能: 支持与主流财务软件（如用友、金蝶）的数据对接，可自动生成工资、成本等财务凭证，实现业财一体化。

第五部分：库存与供应链模块 (店长/总部端)

5.1 库存管理 (ID: INV-05-01)

功能: 对店内零售商品和内部耗材进行全面的出入库、盘点管理。

流程: 管理入库（记录批次效期）、销售出库（自动扣减）、耗材领用（自动扣减）、盘点（生成盘盈盘亏报告）。

字段: 库存ID, 商品ID, 门店ID, 当前数量, 批次号, 有效期。

优化建议: 增加批次追溯: 增加批次使用记录表，详细记录每个批次的产品用于哪个订单的哪个项目，实现从销售到批次的完整追溯。

5.2 智能预警 (ID: INV-05-02)

功能: 自动化监控库存状态，防止断货或物料过期。

规则: 低库存预警 (低于安全库存值) 和 近效期预警 (临近预警天数)。

5.3 门店间调拨 (ID: INV-05-03)

功能: 发起门店间库存调拨申请，接收方确认后完成库存转移。全流程状态可追溯。

第六部分：员工端专属模块 (员工H5端)

6.1 员工工作安排 (ID: SP-06-01)

功能: 以时间轴形式展示每日工作日程表。

内容: 预约服务、会议/培训、顾客随访、其他任务。

6.2 个人业绩与服务记录 (ID: SP-06-02)

功能: 提供实时的、详细的个人业绩和服务历史查询中心，并保护客户隐私。

内容:

服务记录时间轴: 展示 时间, 项目, 店铺, 金额, 本单提成, 备注。顾客姓名显示为“张**”，不显示完整手机号。

工资查询: 可按日或月查看【预估工资】，包含提成、底薪等明细。

6.3 [新增] 假勤与调班 (ID: SP-06-03)

功能: 提供在线的请假和调班申请流程，实现排班管理的自动化闭环。

流程: 员工在H5端提交请假或调班申请 -> 店长在店长端收到通知并进行审批 -> 审批通过后，系统自动更新【自动排班系统】和【可视化预约日历】中的员工可用状态。

字段: 申请ID, 申请人, 申请类型 (事假/病假/调班), 开始/结束时间, 理由, 审批状态。

第七部分：数据决策中心 (店长/股东/总部端)

7.1 数据驾驶舱与报表 (ID: BI-07-01)

功能: 将核心经营指标图表化展示，并提供可钻取的多维度详细报表。

核心报表:

营业分析: 按日/周/月分析门店、品类的收入、订单量。

员工业绩分析: 分析员工的服务人次、实操金额、总提成、好评率。

会员分析: RFM模型分析，洞察会员新增、留存、复购、流失情况，提供客户画像与标签管理。

成本利润分析: 展示门店的营收、耗材成本、运营成本和净利润。

经营指标分析: 提供客单价、客流量、转化率、复购率等关键指标的趋势分析与异动预警。

评价分析: 统计各门店、员工在多平台的好评率和差评原因。

价格-销量分析: 分析独立定价策略对销量和利润的影响。

趋势预测: 基于历史数据，提供未来销售额、客流量的趋势预测。

共同功能: 所有报表均支持按时间、门店等条件筛选，并可导出为Excel文件。

第八部分：六端入口与核心职责
终端	主要用户	核心职责
顾客端	会员	自助预约、查看账户（余额/积分/小票）、评价服务
收银端	收银员	快速开单收银、会员充值、交接班
店长端	店长	实时监控、排班、库存管理、成本录入、员工考核、评价录入、门店独立定价
股东端	投资者	查看投资回报、分红报表、多店对比、成本利润分析、风险预警
员工端	技师/助理	查看工作安排、确认顾客到店、查询个人业绩、为客开单
总部端	管理层	全局管控（门店/项目/规则）、供应链管理、查看全平台报表、权限分配、股东管理
第九部分：非功能性需求与优化建议

9.1 性能优化

数据库索引: 为高频查询字段（如订单状态、预约时间、会员手机号）增加复合索引。

缓存策略: 使用Redis缓存热点数据，如会员信息、项目价格、门店配置等。

API优化: 对返回大量数据的接口进行分页处理；使用批量查询代替循环查询。

高可用架构: 采用冗余设计和自动故障转移机制，确保服务连续性。

9.2 安全性

API安全: 增加API请求频率限制，防止恶意调用。
数据安全:
  - 存储: 对用户手机号、密码等敏感信息进行加密存储。
  - 权限: 实施基于角色的访问控制(RBAC)，支持按门店、数据类型进行精细化权限设置和数据隔离。
  - 合规: 支持数据分类分级管理，满足数据安全与隐私保护的合规性要求。
敏感操作: 对大额充值、批量导出等敏感操作，增加二次验证（如短信验证码）。

9.3 财务严谨性

财务对账: 增加每日对账功能，自动比对系统记录的线上支付金额与第三方支付平台的实际到账金额。

退款流程: 设计严格的退款审批流程，明确不同角色的退款权限和额度。

9.4 运维与监控

系统监控: 建立系统健康检查机制，监控数据库连接、服务器负载、API响应时间等关键指标。
业务监控: 对核心业务指标（如日订单量、充值金额）设置阈值，发生异常时自动告警。
数据备份: 制定详细的数据备份策略（全量+增量、异地+云），并建立定期恢复演练机制。
灾备预案: 制定完善的应急预案和灾难恢复计划。

9.5 代码质量

错误处理: 建立统一的、规范化的错误和异常处理机制。

日志记录: 采用结构化日志，记录详细的请求上下文信息，便于问题排查和数据分析。

插件化架构: 考虑将部分独立功能（如营销活动、优惠券）设计为可插拔的插件，便于未来扩展。
多租户支持: 在数据库设计层面预留租户ID字段，为未来拓展SaaS服务做准备。

9.7 [新增] 生态与开放性

开放API平台: 规划并提供一套标准的Open API，允许系统与第三方应用（如企业微信、钉钉、小程序商城、独立的财务软件）进行安全的数据交换和功能集成。
数据集成: 支持通过ETL工具进行数据抽取、转换和加载，并实施主数据管理，打破数据孤岛。

第十部分：[新增] 面向未来的架构与AI集成

10.1 AI技术应用
- 智能推荐: 基于客户画像与消费习惯，实现个性化项目、技师的智能推荐。
- 智能预测: 通过AI模型预测客户流失风险、预测未来销售趋势，辅助运营决策。
- 智能客服: 应用智能客服机器人处理部分常规咨询与预约，提升服务效率。

10.2 大数据融合
- 精准客户画像: 整合多源数据（消费、行为、评价），构建360°客户视图，实现更精准的客户分群与营销。
- 经营决策支持: 实现从“经验驱动”到“数据驱动”的转变，通过多维度数据分析和对标分析，优化定价、排班、选址等经营决策。

10.3 移动化深化
- O2O深度融合: 实现线上预约、营销、会员权益与线下服务的无缝衔接。
- LBS场景营销: 支持基于位置信息(LBS)的精准营销，如向附近客户推送优惠信息。