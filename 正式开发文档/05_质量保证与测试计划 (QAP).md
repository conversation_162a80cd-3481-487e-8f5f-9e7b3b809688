# 按摩推拿连锁管理系统 - 质量保证与测试计划 (QAP)

**版本**: 1.1
**日期**: 2024-07-26
**关联文档**: PRD v1.0, SDD v1.0

## 变更历史

| 版本 | 日期 | 修改人 | 变更描述 |
| :--- | :--- | :--- | :--- |
| 1.0 | 2024-07-25 | 系统架构师 | 初始版本 |
| 1.1 | 2024-07-26 | 测试经理 | 补充测试数据管理和测试用例设计章节 |

---

## 1. 概述

本质量保证与测试计划 (Quality Assurance and Test Plan, QAP) 旨在为"按摩推拿连锁管理系统"项目定义一套全面的测试策略、流程和标准，以确保最终交付的产品满足产品需求文档（PRD）中定义的功能、性能、安全及其他非功能性要求。

### 1.1 测试目标

- **功能验证**: 确保所有业务功能均按PRD规范正确实现。
- **性能达标**: 保证系统在高并发和大数据量场景下依然性能稳定，满足性能指标。
- **安全可靠**: 发现并修复潜在的安全漏洞，确保用户数据和交易安全。
- **用户体验**: 保证系统界面友好、操作流畅，符合用户使用习惯。
- **稳定性保障**: 确保系统能够长期稳定运行，可用性达到99.9%。

### 1.2 测试范围

测试将覆盖系统的所有"六端"以及后端的全部微服务，包括：

- **前端**: 顾客端小程序、员工端、收银端、店长端、股东端、总部端PC Web。
- **后端**: 所有微服务的功能逻辑和API接口。
- **集成**: 服务与服务之间、前后端之间、与第三方系统（支付、短信等）的集成。
- **非功能性**: 性能、安全、兼容性、可用性等。

---

## 2. 测试策略

### 2.1 测试分层模型（测试金字塔）

我们将采用经典的测试金字塔模型来组织测试活动，合理分配测试资源。

- **单元测试 (Unit Tests) - 占比 ~70%**:
    - **目标**: 验证单个函数、类或模块的正确性。
    - **执行者**: 开发工程师。
    - **工具**: JUnit (Java), Jest/Vitest (Vue), PyTest (Python)。
    - **要求**: 核心业务逻辑的代码覆盖率不低于80%。在CI/CD流水线中强制执行，不通过则无法合并代码。

- **集成测试 (Integration Tests) - 占比 ~20%**:
    - **目标**: 验证服务内部模块间、微服务之间的接口调用和数据交互。
    - **执行者**: 开发工程师、测试工程师。
    - **内容**:
        - **API接口测试**: 验证所有API的请求、响应、鉴权、参数校验是否符合规范。
        - **服务间交互**: 测试跨服务的业务流程，如创建预约后是否正确生成待支付订单。
        - **数据库集成**: 验证数据读写的正确性和一致性。
    - **工具**: Postman/Newman, REST Assured。

- **端到端测试 (End-to-End Tests) - 占比 ~10%**:
    - **目标**: 模拟真实用户场景，验证完整的业务流程是否通畅。
    - **执行者**: 测试工程师。
    - **内容**: 从用户界面发起操作，贯穿前后端，验证整个系统的功能正确性。例如：顾客从微信小程序浏览、选择技师、下单预约、支付、收到通知，技师端接到预约提醒的全过程。
    - **工具**: Cypress, Playwright, Appium (小程序自动化)。

### 2.2 测试类型

| 测试类型 | 描述 | 负责人 | 核心关注点 |
| :--- | :--- | :--- | :--- |
| **功能测试** | 验证系统是否满足PRD中定义的所有功能需求。 | 测试团队 | 业务流程、数据准确性、边界条件、异常处理。 |
| **性能测试** | 评估系统在不同负载下的性能表现。 | 测试团队 | **并发用户数**、**响应时间**、吞吐量(TPS)、资源利用率。 |
| **安全测试** | 发现并修复系统中的安全漏洞。 | 测试/安全团队 | SQL注入、XSS、CSRF、权限绕过、敏感信息泄露。 |
| **兼容性测试** | 确保系统在不同环境下的表现一致。 | 测试团队 | **浏览器兼容性** (Chrome, Firefox, Safari)、**移动端兼容性** (主流iOS/Android版本)、分辨率适配。 |
| **回归测试** | 在代码变更后，确保原有功能未受影响。 | 测试团队 | 核心业务流程的自动化回归测试用例集。 |
| **可用性测试** | 从用户视角评估系统的易用性和体验。 | 产品/测试团队 | 界面布局、操作流程、文案提示是否清晰易懂。 |

---

## 3. 测试环境与数据

### 3.1 环境规划

| 环境名称 | 用途 | 部署方式 | 数据来源 |
| :--- | :--- | :--- | :--- |
| **开发环境 (Dev)** | 开发人员自测 | 本地或云上共享 | 开发者构造 |
| **测试环境 (Test)** | 日常功能与集成测试 | 独立K8s集群 | 脱敏后的生产数据、测试脚本生成 |
| **性能测试环境** | 负载、压力测试 | 独立K8s集群，配置与生产环境1:1或按比例缩放 | 模拟生成的海量数据 |
| **预发布环境 (Staging)** | 上线前最终回归验证 | 与生产环境完全一致 | 生产环境的只读副本或近期备份 |

### 3.2 测试数据管理

- **数据生成**: 开发专门的数据生成工具，能够模拟多门店、多员工、海量会员和订单数据。
- **数据脱敏**: 生产数据在导入测试环境前，必须经过严格的脱敏处理，特别是手机号、姓名、健康档案等敏感信息。

### 3.3 测试用例设计

测试用例设计将遵循以下原则和方法：

- **基于需求的测试**: 每个测试用例必须追溯到PRD中的具体需求点。
- **测试覆盖矩阵**: 建立需求-测试用例覆盖矩阵，确保所有功能点都有对应的测试用例。
- **边界值分析**: 针对数值型输入，设计边界值、极值和无效值的测试用例。
- **等价类划分**: 将输入数据划分为有效和无效等价类，减少冗余测试。
- **决策表技术**: 对于复杂的业务规则和条件组合，使用决策表来设计测试用例。
- **状态转换测试**: 针对有状态的业务流程（如订单、预约状态变化），设计状态转换测试。

#### 3.3.1 核心业务流程测试用例示例

**预约流程测试用例**:

| 用例ID | 用例名称 | 前置条件 | 测试步骤 | 预期结果 | 关联需求 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| TC-AP-001 | 顾客成功预约服务 | 顾客已登录，有可用技师 | 1. 选择服务类型<br>2. 选择技师<br>3. 选择时间段<br>4. 提交预约 | 预约成功创建，状态为"已预约"，顾客收到确认通知 | PRD 3.3 核心运营-可视化预约日历 |
| TC-AP-002 | 预约时间冲突检测 | 技师在指定时间已有预约 | 1. 选择已被预约的技师和时间段<br>2. 提交预约 | 系统提示时间冲突，无法预约 | PRD 3.3 核心运营-可视化预约日历 |
| TC-AP-003 | 离线状态下查看预约 | 技师端App处于离线状态 | 1. 断开网络连接<br>2. 打开技师端App<br>3. 查看今日预约 | 可正常查看之前已同步的预约信息 | PRD 4.1 性能需求-离线能力 |

**收银开单测试用例**:

| 用例ID | 用例名称 | 前置条件 | 测试步骤 | 预期结果 | 关联需求 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| TC-POS-001 | 会员混合支付结算 | 会员有储值余额 | 1. 选择会员<br>2. 添加服务项目<br>3. 选择"储值+微信"支付<br>4. 完成支付 | 订单状态变为"已支付"，会员储值减少，生成电子小票 | PRD 3.4 收银与财务-收银台 |
| TC-POS-002 | 服务中临时加项 | 订单状态为"服务中" | 1. 查找进行中的订单<br>2. 点击"加项"<br>3. 添加新服务项目<br>4. 保存订单 | 订单金额更新，新增项目显示在订单明细中 | PRD 3.4 收银与财务-收银台 |

---

## 4. 关键业务测试方案

### 4.1 性能测试方案
- **目标场景**:
    - **基准测试**: 模拟500并发用户，测试核心接口（登录、获取预约、开单）的平均响应时间和TPS。
    - **负载测试**: 逐步增加并发用户数，从100到2000，观察系统性能拐点。
    - **稳定性测试**: 模拟1000并发用户，持续运行8小时，监控系统资源使用情况和是否存在内存泄漏。
- **关键指标**:
    - 平均响应时间 < 500ms
    - 95分位响应时间 < 1s
    - 服务器CPU/内存使用率 < 75%
    - 核心业务TPS > 500

### 4.2 安全测试方案
- **常规漏洞扫描**: 使用OWASP ZAP、Burp Suite等工具对Web应用进行自动化扫描。
- **渗透测试**: 针对关键业务模块进行手动渗透测试，特别是：
    - **权限校验**: 尝试普通员工访问店长/总部数据，顾客A访问顾客B的数据。
    - **支付流程**: 模拟篡改订单金额、重复支付等攻击。
    - **敏感数据**: 验证健康档案等敏感信息在传输和存储时是否加密。
- **代码审计**: 对核心代码进行安全审计，发现潜在的安全风险。

### 4.3 健康档案模块专项测试

鉴于健康档案涉及敏感个人信息，需进行专项测试：

- **数据加密验证**: 确保健康档案数据在传输和存储过程中均已加密。
- **授权流程测试**: 验证用户授权流程的完整性，确保获取健康信息时有独立的授权弹窗。
- **数据脱敏测试**: 验证在非必要场景下，健康信息是否正确脱敏显示。
- **数据删除权测试**: 验证顾客是否可以通过明确的路径申请删除个人健康数据。

---

## 5. 缺陷管理流程

- **缺陷管理工具**: Jira。
- **缺陷生命周期**: `New` -> `Open` -> `In Progress` -> `Resolved` -> `Re-test` -> `Closed / Re-opened`。
- **缺陷严重等级**:
    - **Blocker (阻塞)**: 系统崩溃、核心流程无法走通。
    - **Critical (严重)**: 主要功能严重错误、数据丢失或安全漏洞。
    - **Major (主要)**: 功能部分失效或与需求不符。
    - **Minor (次要)**: UI显示错误、提示信息不友好等。
    - **Trivial (轻微)**: 拼写错误、格式不规范等。
- **缺陷报告规范**: 包含标题、复现步骤、期望结果、实际结果、截图/日志、环境信息等。

### 5.1 缺陷优先级定义

| 优先级 | 定义 | 修复时间要求 |
| :--- | :--- | :--- |
| **P0** | 阻塞发布的严重问题，必须立即修复 | 24小时内 |
| **P1** | 高优先级问题，严重影响用户体验 | 3天内 |
| **P2** | 中等优先级，有影响但有临时解决方案 | 当前迭代内 |
| **P3** | 低优先级，小问题或优化建议 | 下一迭代或适当安排 |

---

## 6. 自动化测试规划

- **UI自动化**: 使用Cypress/Playwright对总部PC端的核心业务流程编写自动化脚本，纳入回归测试。
- **API自动化**: 使用Postman/Newman编写全面的API测试脚本集合，覆盖所有API接口，在CI/CD流程中自动执行。
- **小程序自动化**: 使用Appium或微信官方提供的小程序自动化工具，对顾客端核心流程（预约、支付）进行自动化测试。

### 6.1 自动化测试实施路线

| 阶段 | 时间 | 目标 | 关键任务 |
| :--- | :--- | :--- | :--- |
| **第一阶段** | MVP前 | 建立基础框架 | 搭建自动化测试框架，编写核心API测试用例 |
| **第二阶段** | MVP-V1.1 | 扩展覆盖范围 | 增加UI自动化测试，覆盖核心业务流程 |
| **第三阶段** | V1.1-V2.0 | 全面自动化 | 实现端到端测试自动化，建立持续集成测试流水线 |

## 7. 测试交付物

- **测试计划**: 详细的测试活动计划，包括范围、资源、时间表。
- **测试用例**: 功能测试用例、性能测试方案、安全测试清单。
- **自动化测试脚本**: API测试脚本、UI测试脚本。
- **测试报告**: 每次测试执行后的详细报告，包括测试结果、缺陷统计、风险评估。
- **质量评估报告**: 在每个里程碑发布前提供的系统质量评估报告。 