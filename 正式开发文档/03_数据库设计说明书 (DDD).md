# 按摩推拿连锁管理系统 - 数据库设计说明书 (DDD)

**版本**: 1.1
**日期**: 2024-07-26
**关联文档**: PRD v1.0, SDD v1.0

## 变更历史

| 版本 | 日期 | 修改人 | 变更描述 |
| :--- | :--- | :--- | :--- |
| 1.0 | 2024-07-25 | 数据库架构师 | 初始版本 |
| 1.1 | 2024-07-26 | 数据库架构师 | 修复内容截断问题 |

---

## 1. 概述

本数据库设计说明书 (Database Design Document, DDD) 旨在详细阐述"按摩推拿连锁管理系统"的数据存储结构、实体关系、字段定义及设计约束。本文档是系统设计说明书 (SDD) 的具象化，将作为后端开发和数据管理的直接依据。

### 1.1 设计原则
- **微服务数据库分离**: 根据SDD中的微服务划分，每个核心服务拥有独立的数据库Schema，以实现松耦合和独立部署。服务间的数据交互通过API进行。
- **统一命名规范**:
    - 表名: 统一使用小写，单词间用下划线分隔，并以 `tbl_` 为前缀，如 `tbl_users`。
    - 字段名: 统一使用小写，单词间用下划线分隔，如 `customer_id`。
    - 索引: 普通索引以 `idx_` 开头，唯一索引以 `uk_` 开头。
- **公共字段**: 核心业务表应包含以下公共字段，以支持多租户、数据审计和软删除。
    - `id` (BIGINT, PK, Auto-Increment): 主键。
    - `tenant_id` (BIGINT): 租户ID，用于SaaS数据隔离。
    - `store_id` (BIGINT): 门店ID，用于连锁店数据隔离。
    - `created_at` (DATETIME): 创建时间。
    - `updated_at` (DATETIME): 更新时间。
    - `is_deleted` (TINYINT): 软删除标记 (0: 未删除, 1: 已删除)。
- **数据类型选择**: 根据业务需求和性能考量，选择最合适的数据类型。例如，价格和金额使用`DECIMAL(10, 2)`以保证精度。
- **索引策略**: 为所有外键（Foreign Key）和高频查询条件的字段建立索引，以优化查询性能。

---

## 2. 总体E-R图 (Entity-Relationship Diagram)

下图展示了系统核心实体之间的高层关系。

```mermaid
erDiagram
    USERS ||--o{ EMPLOYEES : "is a"
    USERS ||--o{ CUSTOMERS : "is a"
    CUSTOMERS ||--|{ MEMBERSHIPS : "has"
    CUSTOMERS ||--o{ APPOINTMENTS : "makes"
    CUSTOMERS ||--o{ ORDERS : "places"
    EMPLOYEES ||--o{ APPOINTMENTS : "serves"
    EMPLOYEES ||--o{ SCHEDULES : "has"
    STORES ||--o{ EMPLOYEES : "employs"
    STORES ||--o{ APPOINTMENTS : "hosts"
    APPOINTMENTS }o--|| SERVICES : "books"
    ORDERS ||--|{ ORDER_ITEMS : "contains"
    SERVICES ||--o{ ORDER_ITEMS : "is"
    PRODUCTS ||--o{ ORDER_ITEMS : "is"
    PRODUCTS ||--|{ INVENTORY : "stocks"
    STORES ||--|{ INVENTORY : "has"

    USERS {
        bigint id PK
        varchar username
        varchar password_hash
        varchar user_type
    }

    CUSTOMERS {
        bigint id PK
        bigint user_id FK
        varchar name
        varchar phone
    }

    EMPLOYEES {
        bigint id PK
        bigint user_id FK
        varchar name
        bigint store_id FK
    }

    STORES {
        bigint id PK
        varchar name
        varchar address
    }

    APPOINTMENTS {
        bigint id PK
        bigint customer_id FK
        bigint employee_id FK
        bigint service_id FK
        datetime start_time
    }

    ORDERS {
        bigint id PK
        bigint customer_id FK
        decimal final_amount
        varchar status
    }

    SERVICES {
        bigint id PK
        varchar name
        decimal price
    }

    PRODUCTS {
        bigint id PK
        varchar name
        varchar type
    }

    INVENTORY {
        bigint id PK
        bigint product_id FK
        bigint store_id FK
        int quantity
    }
```

---

## 3. 各微服务数据模型详设

### 3.1 用户与权限服务 (User & Auth Service)

**Schema**: `db_user_auth`

该服务负责管理所有类型的用户、员工、顾客、门店、角色和权限。

**Table: `tbl_users` (用户主表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `username` | VARCHAR(50) | UK | 登录名，可以是手机号或自定义账号 |
| `phone` | VARCHAR(20) | UK | 手机号，用于登录和通知 |
| `password_hash` | VARCHAR(255) | | 加密后的密码 |
| `user_type` | VARCHAR(20) | | 用户类型 (customer, employee, shareholder, admin) |
| `status` | TINYINT | | 状态 (1:正常, 2:禁用) |
| ... | ... | | (公共字段) |

**Table: `tbl_employees` (员工档案表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `user_id` | BIGINT | UK, FK | 关联`tbl_users`.`id` |
| `employee_code` | VARCHAR(50) | | 员工工号 |
| `full_name` | VARCHAR(50) | | 员工姓名 |
| `position` | VARCHAR(50) | | 职位 (技师, 店长, 收银) |
| `skill_tags` | JSON | | 技能标签, e.g., `["推拿", "艾灸"]` |
| `hire_date` | DATE | | 入职日期 |
| ... | ... | | (公共字段, 含`store_id`) |

**Table: `tbl_customers` (顾客档案表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `user_id` | BIGINT | UK, FK | 关联`tbl_users`.`id` |
| `nick_name` | VARCHAR(50) | | 顾客昵称 |
| `gender` | TINYINT | | 性别 (0:未知, 1:男, 2:女) |
| `birthday` | DATE | | 生日 |
| `avatar_url` | VARCHAR(255) | | 头像URL |
| `source` | VARCHAR(50) | | 客户来源 (小程序注册, 门店创建) |
| ... | ... | | (公共字段, 含`store_id` of first store) |

**其他核心表**: `tbl_stores` (门店信息), `tbl_shareholders` (股东信息), `tbl_roles` (角色), `tbl_permissions` (权限), `tbl_user_roles` (用户角色关系), `tbl_role_permissions` (角色权限关系)。

---

### 3.2 会员与营销服务 (Member & Marketing Service)

**Schema**: `db_member_marketing`

负责管理会员、健康档案、营销活动等。

**Table: `tbl_memberships` (会员信息表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `customer_id` | BIGINT | UK, FK | 关联`tbl_customers`.`id` |
| `card_number` | VARCHAR(50) | UK | 会员卡号 |
| `level_id` | BIGINT | FK | 会员等级ID |
| `balance` | DECIMAL(10, 2) | | 储值余额 |
| `points` | INT | | 积分 |
| `status` | TINYINT | | 状态 (1:正常, 2:冻结) |
| ... | ... | | (公共字段) |

**Table: `tbl_health_records` (顾客健康档案)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `customer_id` | BIGINT | UK, FK | 关联`tbl_customers`.`id` |
| `chief_complaint` | TEXT | | 主诉 |
| `body_map_data` | JSON | | 可视化身体图谱标记数据 |
| `preferences` | TEXT | | 调理偏好 (力度、关注部位等) |
| `contraindications`| TEXT | | 禁忌症 |
| `last_updated_by`| BIGINT | FK | 最后更新员工ID |
| ... | ... | | (公共字段) |

**其他核心表**: `tbl_membership_levels` (会员等级), `tbl_balance_logs` (余额变动记录), `tbl_points_logs` (积分变动记录), `tbl_coupons` (优惠券模板), `tbl_customer_coupons` (用户持有优惠券)。

---

### 3.3 预约与排班服务 (Appointment & Scheduling Service)

**Schema**: `db_appointment_schedule`

系统的核心调度中心。

**Table: `tbl_appointments` (预约记录表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `appointment_no` | VARCHAR(50) | UK | 预约单号 |
| `customer_id` | BIGINT | FK | 顾客ID |
| `employee_id` | BIGINT | FK | 技师ID |
| `service_id` | BIGINT | FK | 服务项目ID |
| `resource_id` | BIGINT | FK | 房间/设备ID (可选) |
| `start_time` | DATETIME | | 预约开始时间 |
| `end_time` | DATETIME | | 预约结束时间 |
| `status` | VARCHAR(20) | | 状态 (scheduled, confirmed, in_progress, completed, cancelled, no_show) |
| ... | ... | | (公共字段, 含`store_id`) |

**Table: `tbl_services` (服务项目表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `name` | VARCHAR(100) | | 服务名称 |
| `description` | TEXT | | 描述 |
| `duration_minutes`| INT | | 服务时长（分钟） |
| `price` | DECIMAL(10, 2) | | 标准价格 |
| `consumables_bom`| JSON | | 耗材BOM清单, e.g., `[{"product_id": 101, "quantity": 2}]` |
| ... | ... | | (公共字段) |

**Table: `tbl_schedules` (员工排班表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `employee_id` | BIGINT | FK | 员工ID |
| `schedule_date` | DATE | | 排班日期 |
| `start_time` | TIME | | 上班时间 |
| `end_time` | TIME | | 下班时间 |
| `schedule_type` | VARCHAR(20) | | 类型 (work, rest, leave) |
| ... | ... | | (公共字段, 含`store_id`) |

**其他核心表**: `tbl_resources` (可预约资源，如房间、设备)。

---

### 3.4 订单与支付服务 (Order & Payment Service)

**Schema**: `db_order_payment`

负责处理交易、支付、退款等。

**Table: `tbl_orders` (订单主表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `order_no` | VARCHAR(50) | UK | 订单号 |
| `customer_id` | BIGINT | FK | 顾客ID (散客可为空) |
| `appointment_id` | BIGINT | FK | 关联预约ID (可选) |
| `original_amount`| DECIMAL(10, 2) | | 订单原价 |
| `discount_amount`| DECIMAL(10, 2) | | 折扣金额 |
| `final_amount` | DECIMAL(10, 2) | | 应付金额 |
| `status` | VARCHAR(20) | | 状态 (pending_payment, paid, completed, closed, refunded) |
| `created_by` | BIGINT | FK | 开单员工ID |
| ... | ... | | (公共字段, 含`store_id`) |

**Table: `tbl_order_items` (订单明细表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `order_id` | BIGINT | FK | 关联`tbl_orders`.`id` |
| `item_type` | VARCHAR(20) | | 项目类型 (service, product, membership_recharge) |
| `item_id` | BIGINT | | 关联的服务/商品/会员卡ID |
| `item_name` | VARCHAR(100) | | 项目名称 |
| `price` | DECIMAL(10, 2) | | 单价 |
| `quantity` | INT | | 数量 |
| `line_total` | DECIMAL(10, 2) | | 该行总价 |
| `assigned_employee_id`| BIGINT | FK | 服务技师ID (仅服务类) |
| ... | ... | | (公共字段) |

**Table: `tbl_payments` (支付记录表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `order_id` | BIGINT | FK | 关联`tbl_orders`.`id` |
| `payment_method` | VARCHAR(30) | | 支付方式 (balance, wechat_pay, alipay, cash, card) |
| `amount` | DECIMAL(10, 2) | | 支付金额 |
| `transaction_no`| VARCHAR(100) | | 第三方支付流水号 |
| `status` | VARCHAR(20) | | 状态 (succeeded, failed, pending) |
| ... | ... | | (公共字段) |

**其他核心表**: `tbl_refunds` (退款单)。

---

### 3.5 库存与供应链服务 (Inventory & Supply Chain Service)

**Schema**: `db_inventory_supply`

管理实物商品和耗材。

**Table: `tbl_products` (产品/物料表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `product_code` | VARCHAR(50) | UK | 产品编码/SKU |
| `name` | VARCHAR(100) | | 产品名称 |
| `type` | VARCHAR(20) | | 类型 (retail:零售商品, consumable:耗材) |
| `specifications` | VARCHAR(100) | | 规格 |
| `unit` | VARCHAR(10) | | 单位 (个, 瓶, 克) |
| `cost_price` | DECIMAL(10, 2) | | 成本价 |
| `sale_price` | DECIMAL(10, 2) | | 零售价 (若可售) |
| ... | ... | | (公共字段) |

**Table: `tbl_inventory` (库存表)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `product_id` | BIGINT | FK | 关联`tbl_products`.`id` |
| `quantity_on_hand`| INT | | 当前库存数量 |
| `safety_stock` | INT | | 安全库存阈值 |
| `uk_product_store` | UNIQUE | | `(product_id, store_id)` |
| ... | ... | | (公共字段, 含`store_id`) |

**其他核心表**: `tbl_inventory_logs` (出入库流水), `tbl_stocktakes` (盘点单), `tbl_transfers` (调拨单)。

---

### 3.6 数据仓库 (OLAP) 设计

数据仓库将采用 **ClickHouse** 或类似列式存储数据库，其设计遵循星型模型（Star Schema）。

- **事实表 (Fact Tables)**:
    - `fact_sales`: 记录每一笔销售明细，包含金额、成本、利润、时间、顾客、员工、门店等维度的外键。
    - `fact_appointments`: 记录每一次预约，包含时长、状态、顾客、员工、门店等维度的外键。
- **维度表 (Dimension Tables)**:
    - `dim_time`: 时间维度表，包含年、月、日、星期、节假日等信息。
    - `dim_customers`: 顾客维度表，包含顾客的静态信息。
    - `dim_employees`: 员工维度表。
    - `dim_stores`: 门店维度表。
    - `dim_services`: 服务维度表。
    - `dim_products`: 产品维度表。

数据将通过监听业务库（MySQL）的Binlog，经由ETL工具（如Canal + Flink/DataX）清洗、转换后，准实时地加载到ClickHouse中，以支持BI报表和数据分析的高性能查询。

### 3.7 中医特色健康档案扩展设计

为了更好地支持按摩推拿行业的专业性需求，健康档案模块进行了中医特色扩展：

**Table: `tbl_health_records_tcm` (中医健康档案扩展)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `health_record_id` | BIGINT | FK | 关联基础健康档案ID |
| `constitution_type` | VARCHAR(50) | | 体质类型 (阴虚、阳虚、气虚等) |
| `meridian_diagnosis` | JSON | | 经络诊断数据，记录各经络异常点 |
| `treatment_history` | JSON | | 历次调理记录，包含手法、穴位、效果评估 |
| `prescription` | TEXT | | 推荐的调理方案或药膳处方 |
| ... | ... | | (公共字段) |

**Table: `tbl_acupoints` (穴位参考库)**
| 字段名 | 数据类型 | 约束 | 备注 |
| :--- | :--- | :--- | :--- |
| `id` | BIGINT | PK, AI | 主键 |
| `code` | VARCHAR(20) | UK | 穴位编码 |
| `name_zh` | VARCHAR(50) | | 中文名称 |
| `name_en` | VARCHAR(100) | | 英文名称 |
| `meridian_id` | BIGINT | FK | 所属经络ID |
| `location` | TEXT | | 穴位定位说明 |
| `indications` | TEXT | | 主治功效 |
| `cautions` | TEXT | | 注意事项 |
| `image_url` | VARCHAR(255) | | 穴位图片URL |
| ... | ... | | (公共字段) |