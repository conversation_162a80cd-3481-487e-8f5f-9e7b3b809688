# T007 - 库存与供应链管理

## 任务概述

开发库存管理、智能预警、门店间调拨等供应链管理功能。建立完整的库存管理体系，确保门店运营所需的耗材和商品供应充足，优化库存成本，提高供应链效率。

## 详细需求描述

### 1. 库存基础管理
- **商品分类管理**:
  - 耗材类: 按摩油、精油、一次性用品等
  - 零售商品: 保健品、护理用品、礼品等
  - 设备器材: 按摩器具、理疗设备等
  - 办公用品: 文具、清洁用品等
- **商品档案管理**:
  - 基本信息: 商品名称、规格、单位、条码
  - 供应商信息: 供应商、采购价格、供货周期
  - 库存信息: 安全库存、最大库存、当前库存
  - 财务信息: 成本价、零售价、毛利率
- **多仓库管理**:
  - 总仓管理: 总部中央仓库管理
  - 门店仓库: 各门店独立库存管理
  - 虚拟仓库: 供应商直发等虚拟库存
  - 仓库间关系: 上下级仓库调拨关系

### 2. 入库管理
- **采购入库**:
  - 采购订单: 基于需求生成采购订单
  - 到货验收: 商品到货数量、质量验收
  - 入库登记: 批次管理、保质期管理
  - 成本核算: 采购成本、运费分摊
- **调拨入库**:
  - 门店间调拨: 门店之间的商品调拨
  - 总仓配送: 总仓向门店的配送
  - 退货入库: 客户退货商品入库
  - 盘盈入库: 盘点发现的盈余商品
- **入库单据管理**:
  - 入库单生成: 自动生成入库单据
  - 单据审核: 入库单据审核流程
  - 成本更新: 入库后成本价格更新
  - 财务对接: 与财务系统的数据对接

### 3. 出库管理
- **销售出库**:
  - 服务耗材: 服务过程中的耗材消耗
  - 零售销售: 客户购买商品的出库
  - 自动扣减: 订单完成后自动扣减库存
  - 批次追踪: 出库商品的批次追踪
- **调拨出库**:
  - 门店申请: 门店申请调拨商品
  - 审批流程: 调拨申请的审批流程
  - 出库配送: 商品出库和配送安排
  - 在途管理: 调拨商品的在途跟踪
- **其他出库**:
  - 报损出库: 过期、损坏商品的报损
  - 赠送出库: 促销赠品的出库管理
  - 样品出库: 试用样品的出库管理
  - 盘亏出库: 盘点发现的亏损商品

### 4. 智能预警系统
- **库存预警**:
  - 低库存预警: 库存低于安全库存时预警
  - 零库存预警: 商品库存为零时紧急预警
  - 高库存预警: 库存过高时的积压预警
  - 滞销预警: 长期无销售的商品预警
- **保质期预警**:
  - 临期预警: 商品接近保质期时预警
  - 过期预警: 商品已过保质期时预警
  - 批次管理: 按批次管理商品保质期
  - 处理建议: 临期商品的处理建议
- **异常预警**:
  - 异常消耗: 商品消耗异常时预警
  - 盘点差异: 盘点发现差异时预警
  - 成本异常: 商品成本异常变动预警
  - 供应异常: 供应商供货异常预警

### 5. 门店间调拨
- **调拨需求管理**:
  - 需求申请: 门店提交调拨需求申请
  - 需求分析: 分析调拨需求的合理性
  - 供应匹配: 匹配有库存的门店或仓库
  - 调拨计划: 制定最优的调拨计划
- **调拨执行流程**:
  - 调拨单生成: 生成调拨单据
  - 出库操作: 调出方执行出库操作
  - 物流跟踪: 调拨商品的物流跟踪
  - 入库确认: 调入方确认收货入库
- **调拨成本管理**:
  - 运费计算: 调拨运费的计算分摊
  - 成本转移: 调拨商品成本的转移
  - 损耗处理: 调拨过程中损耗的处理
  - 财务结算: 调拨的财务结算处理

### 6. 库存盘点
- **盘点计划管理**:
  - 盘点周期: 定期盘点和临时盘点
  - 盘点范围: 全盘、抽盘、循环盘点
  - 盘点人员: 盘点人员安排和权限
  - 盘点时间: 盘点时间安排和冻结
- **盘点执行**:
  - 盘点单生成: 生成盘点清单
  - 现场盘点: 移动端盘点操作
  - 差异记录: 盘点差异的记录
  - 复盘确认: 差异商品的复盘确认
- **盘点结果处理**:
  - 差异分析: 盘点差异的原因分析
  - 调整处理: 库存数量的调整处理
  - 责任追究: 差异责任的追究处理
  - 报告生成: 盘点结果报告生成

### 7. 供应商管理
- **供应商档案**:
  - 基本信息: 供应商名称、联系方式、地址
  - 资质信息: 营业执照、生产许可证等
  - 合作信息: 合作历史、信用等级、付款方式
  - 商品信息: 供应商提供的商品清单
- **供应商评估**:
  - 质量评估: 商品质量评估和记录
  - 服务评估: 供货及时性、服务态度评估
  - 价格评估: 价格竞争力评估
  - 综合评分: 供应商综合评分排名
- **采购管理**:
  - 询价比价: 多供应商询价比价
  - 采购订单: 采购订单生成和管理
  - 合同管理: 采购合同签订和管理
  - 付款管理: 采购付款计划和执行

## 技术实现要点

### 1. 库存管理核心实现
```java
@Entity
@Table(name = "inventory")
public class Inventory extends BaseEntity {
    private Long productId;
    private Long warehouseId;
    private BigDecimal currentStock;
    private BigDecimal safetyStock;
    private BigDecimal maxStock;
    private BigDecimal avgCost;
    private LocalDateTime lastUpdateTime;
    
    @OneToMany(mappedBy = "inventory")
    private List<InventoryBatch> batches;
}

@Entity
public class InventoryBatch {
    private Long inventoryId;
    private String batchNo;
    private BigDecimal quantity;
    private BigDecimal cost;
    private LocalDate productionDate;
    private LocalDate expiryDate;
    private BatchStatus status;
}

@Service
public class InventoryService {
    
    @Transactional
    public void updateInventory(Long productId, Long warehouseId, 
                               BigDecimal quantity, InventoryChangeType type) {
        Inventory inventory = inventoryRepository
            .findByProductIdAndWarehouseId(productId, warehouseId);
        
        if (inventory == null) {
            inventory = createNewInventory(productId, warehouseId);
        }
        
        // 更新库存数量
        BigDecimal newStock = calculateNewStock(inventory.getCurrentStock(), quantity, type);
        inventory.setCurrentStock(newStock);
        inventory.setLastUpdateTime(LocalDateTime.now());
        
        inventoryRepository.save(inventory);
        
        // 记录库存变动
        recordInventoryChange(inventory, quantity, type);
        
        // 检查库存预警
        checkInventoryAlert(inventory);
    }
}
```

### 2. 智能预警系统
```java
@Component
public class InventoryAlertService {
    
    @Scheduled(fixedRate = 300000) // 每5分钟检查一次
    public void checkInventoryAlerts() {
        List<Inventory> inventories = inventoryRepository.findAll();
        
        for (Inventory inventory : inventories) {
            checkLowStockAlert(inventory);
            checkHighStockAlert(inventory);
            checkExpiryAlert(inventory);
        }
    }
    
    private void checkLowStockAlert(Inventory inventory) {
        if (inventory.getCurrentStock().compareTo(inventory.getSafetyStock()) <= 0) {
            InventoryAlert alert = InventoryAlert.builder()
                .inventoryId(inventory.getId())
                .alertType(AlertType.LOW_STOCK)
                .currentStock(inventory.getCurrentStock())
                .safetyStock(inventory.getSafetyStock())
                .severity(AlertSeverity.HIGH)
                .build();
                
            alertRepository.save(alert);
            notificationService.sendLowStockAlert(alert);
        }
    }
    
    private void checkExpiryAlert(Inventory inventory) {
        LocalDate alertDate = LocalDate.now().plusDays(30); // 30天内到期预警
        
        List<InventoryBatch> expiringBatches = inventory.getBatches().stream()
            .filter(batch -> batch.getExpiryDate().isBefore(alertDate))
            .collect(Collectors.toList());
            
        if (!expiringBatches.isEmpty()) {
            ExpiryAlert alert = ExpiryAlert.builder()
                .inventoryId(inventory.getId())
                .expiringBatches(expiringBatches)
                .alertDate(LocalDate.now())
                .build();
                
            alertRepository.save(alert);
            notificationService.sendExpiryAlert(alert);
        }
    }
}
```

### 3. 门店间调拨实现
```java
@Entity
public class TransferOrder extends BaseEntity {
    private String transferNo;
    private Long fromWarehouseId;
    private Long toWarehouseId;
    private TransferStatus status;
    private BigDecimal totalAmount;
    private LocalDateTime requestTime;
    private LocalDateTime approveTime;
    private LocalDateTime shipTime;
    private LocalDateTime receiveTime;
    
    @OneToMany(mappedBy = "transferOrder", cascade = CascadeType.ALL)
    private List<TransferOrderItem> items;
}

@Service
public class TransferService {
    
    @Transactional
    public TransferOrder createTransferOrder(CreateTransferRequest request) {
        // 验证调拨请求
        validateTransferRequest(request);
        
        // 检查库存可用性
        checkInventoryAvailability(request);
        
        // 创建调拨单
        TransferOrder transferOrder = TransferOrder.builder()
            .transferNo(generateTransferNo())
            .fromWarehouseId(request.getFromWarehouseId())
            .toWarehouseId(request.getToWarehouseId())
            .status(TransferStatus.PENDING)
            .requestTime(LocalDateTime.now())
            .build();
            
        // 添加调拨明细
        List<TransferOrderItem> items = request.getItems().stream()
            .map(item -> createTransferItem(transferOrder, item))
            .collect(Collectors.toList());
        transferOrder.setItems(items);
        
        transferOrder = transferOrderRepository.save(transferOrder);
        
        // 发送审批通知
        notificationService.sendTransferApprovalNotification(transferOrder);
        
        return transferOrder;
    }
    
    @Transactional
    public void approveTransfer(Long transferOrderId, Long approverId) {
        TransferOrder transferOrder = transferOrderRepository.findById(transferOrderId);
        
        // 再次检查库存
        checkInventoryAvailability(transferOrder);
        
        // 预留库存
        reserveInventoryForTransfer(transferOrder);
        
        // 更新状态
        transferOrder.setStatus(TransferStatus.APPROVED);
        transferOrder.setApproveTime(LocalDateTime.now());
        
        transferOrderRepository.save(transferOrder);
        
        // 通知发货
        notificationService.sendShipmentNotification(transferOrder);
    }
}
```

### 4. 库存盘点实现
```java
@Entity
public class StockTaking extends BaseEntity {
    private String takingNo;
    private Long warehouseId;
    private StockTakingType type; // 全盘、抽盘、循环盘
    private StockTakingStatus status;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Long operatorId;
    
    @OneToMany(mappedBy = "stockTaking", cascade = CascadeType.ALL)
    private List<StockTakingItem> items;
}

@Service
public class StockTakingService {
    
    public StockTaking createStockTaking(CreateStockTakingRequest request) {
        StockTaking stockTaking = StockTaking.builder()
            .takingNo(generateTakingNo())
            .warehouseId(request.getWarehouseId())
            .type(request.getType())
            .status(StockTakingStatus.CREATED)
            .startTime(LocalDateTime.now())
            .operatorId(request.getOperatorId())
            .build();
            
        // 生成盘点清单
        List<StockTakingItem> items = generateStockTakingItems(stockTaking, request);
        stockTaking.setItems(items);
        
        // 冻结库存
        freezeInventoryForTaking(stockTaking);
        
        return stockTakingRepository.save(stockTaking);
    }
    
    @Transactional
    public void submitStockTaking(Long stockTakingId, List<StockTakingResult> results) {
        StockTaking stockTaking = stockTakingRepository.findById(stockTakingId);
        
        // 更新盘点结果
        updateStockTakingResults(stockTaking, results);
        
        // 计算差异
        List<StockDifference> differences = calculateStockDifferences(stockTaking);
        
        // 处理差异
        processStockDifferences(differences);
        
        // 更新状态
        stockTaking.setStatus(StockTakingStatus.COMPLETED);
        stockTaking.setEndTime(LocalDateTime.now());
        
        stockTakingRepository.save(stockTaking);
        
        // 解冻库存
        unfreezeInventoryAfterTaking(stockTaking);
    }
}
```

## 验收标准

### 功能验收
- [ ] 库存基础管理功能完整
- [ ] 入库出库流程正常工作
- [ ] 智能预警及时准确触发
- [ ] 门店间调拨流程顺畅
- [ ] 库存盘点功能完善
- [ ] 供应商管理功能齐全

### 业务验收
- [ ] 库存准确率 > 98%
- [ ] 预警及时率 > 95%
- [ ] 调拨效率提升 > 30%
- [ ] 库存周转率提升 > 20%
- [ ] 缺货率 < 2%

### 性能验收
- [ ] 库存查询响应时间 < 300ms
- [ ] 库存更新响应时间 < 500ms
- [ ] 支持10万+商品库存管理
- [ ] 盘点处理时间 < 2小时

## 工作量评估

- **开发工作量**: 20人天
  - 库存基础管理: 4人天
  - 入库出库管理: 4人天
  - 智能预警系统: 3人天
  - 门店间调拨: 4人天
  - 库存盘点: 3人天
  - 供应商管理: 2人天

- **测试工作量**: 8人天
  - 功能测试: 4人天
  - 业务流程测试: 3人天
  - 性能测试: 1人天

- **总工作量**: 28人天

## 依赖关系

### 前置条件
- [x] T001: 项目架构设计与基础设施搭建
- [x] T002: 用户认证与权限管理系统
- [x] T003: 基础管理模块开发

### 后置任务
- T006: 收银与财务管理系统
- T010: 数据分析与决策中心

## 风险点和注意事项

### 业务风险
1. **库存准确性**: 库存数据不准确影响业务运营
   - **缓解措施**: 严格的入库出库流程，定期盘点

2. **供应链中断**: 供应商供货中断影响运营
   - **缓解措施**: 多供应商策略，安全库存设置

### 技术风险
1. **数据一致性**: 分布式环境下库存数据一致性
   - **缓解措施**: 分布式锁，事务处理

2. **性能问题**: 大量库存数据可能影响查询性能
   - **缓解措施**: 数据分片，索引优化

## 关键里程碑

1. **第1-2周**: 完成库存基础管理和入库出库
2. **第3周**: 完成智能预警和门店调拨
3. **第4周**: 完成库存盘点和供应商管理

## 交付物

1. **库存管理服务**: 完整的库存管理微服务
2. **库存管理界面**: Web端库存管理界面
3. **移动端盘点**: 移动端库存盘点应用
4. **预警系统**: 智能预警和通知系统
5. **供应商门户**: 供应商协作门户
6. **库存报表**: 各类库存分析报表
