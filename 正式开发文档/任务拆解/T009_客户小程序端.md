# T009 - 客户小程序端

## 任务概述

开发客户微信小程序，包括在线预约、账户管理、家庭共享等功能。为客户提供便捷的移动服务入口，提升客户体验，增强客户粘性，实现线上线下一体化服务。

## 详细需求描述

### 1. 首页与导航
- **首页设计**:
  - 门店展示: 门店图片、地址、营业时间
  - 服务推荐: 热门服务项目推荐
  - 优惠活动: 当前优惠活动和促销信息
  - 快捷入口: 预约、充值、会员中心等快捷入口
- **底部导航**:
  - 首页: 门店信息和服务推荐
  - 预约: 在线预约服务
  - 会员: 会员中心和账户管理
  - 我的: 个人信息和设置
- **搜索功能**:
  - 服务搜索: 搜索服务项目和技师
  - 门店搜索: 搜索附近门店
  - 历史搜索: 搜索历史记录
  - 热门搜索: 热门搜索关键词

### 2. 在线预约系统
- **服务选择**:
  - 服务分类: 按摩、推拿、理疗等分类浏览
  - 服务详情: 服务介绍、时长、价格、适用人群
  - 技师选择: 技师介绍、技能标签、客户评价
  - 套餐组合: 多项服务的套餐组合
- **时间选择**:
  - 日历视图: 可预约日期的日历展示
  - 时段选择: 可预约时间段选择
  - 实时更新: 预约时间的实时可用性更新
  - 智能推荐: 基于历史偏好的时间推荐
- **预约确认**:
  - 预约信息: 服务、技师、时间确认
  - 价格计算: 原价、折扣、最终价格
  - 支付方式: 储值卡、微信支付等
  - 预约成功: 预约成功通知和提醒设置

### 3. 会员中心
- **会员信息**:
  - 基本信息: 姓名、手机、生日、性别
  - 会员等级: 当前等级、升级条件、等级权益
  - 积分余额: 当前积分、积分获取和使用记录
  - 储值余额: 储值卡余额、充值记录、消费记录
- **会员权益**:
  - 等级特权: 不同等级的专属权益
  - 生日特权: 生日月专属优惠
  - 积分商城: 积分兑换商品和服务
  - 专属客服: VIP客户专属客服
- **充值中心**:
  - 充值套餐: 不同充值金额的优惠套餐
  - 支付方式: 微信支付、银行卡支付
  - 充值记录: 历史充值记录查询
  - 赠送规则: 充值赠送规则说明

### 4. 健康档案管理
- **个人档案**:
  - 基础信息: 身高、体重、年龄、职业
  - 健康状况: 慢性病、过敏史、用药情况
  - 生活习惯: 运动习惯、饮食偏好、作息时间
  - 调理目标: 健康改善目标和期望
- **身体图谱**:
  - 可视化标记: 在人体图上标记不适部位
  - 症状描述: 详细描述症状和感受
  - 严重程度: 不适程度的量化评估
  - 历史变化: 症状变化的历史记录
- **调理记录**:
  - 服务记录: 历次服务的详细记录
  - 效果评估: 服务效果的自我评估
  - 技师建议: 技师的专业建议记录
  - 改善跟踪: 健康状况的改善跟踪

### 5. 家庭共享功能
- **家庭账户**:
  - 家庭成员: 添加和管理家庭成员
  - 权限设置: 不同成员的使用权限
  - 消费限额: 家庭成员的消费限额设置
  - 账单管理: 家庭消费账单统一管理
- **共享储值卡**:
  - 余额共享: 家庭成员共享储值卡余额
  - 消费记录: 各成员的消费记录查询
  - 充值管理: 家庭储值卡的充值管理
  - 使用限制: 储值卡使用规则和限制
- **预约管理**:
  - 代为预约: 为家庭成员代为预约
  - 预约提醒: 家庭成员预约的提醒通知
  - 取消变更: 预约的取消和变更管理
  - 服务记录: 家庭成员的服务记录

### 6. 优惠券与活动
- **优惠券中心**:
  - 可用优惠券: 当前可用的优惠券列表
  - 已使用券: 已使用的优惠券记录
  - 已过期券: 过期优惠券查询
  - 券包推荐: 推荐适合的优惠券包
- **活动参与**:
  - 拼团活动: 参与和发起拼团活动
  - 限时折扣: 限时折扣活动参与
  - 签到有礼: 每日签到获得奖励
  - 邀请有礼: 邀请好友注册获得奖励
- **营销推送**:
  - 个性化推荐: 基于偏好的个性化推荐
  - 活动通知: 新活动和优惠的及时通知
  - 生日关怀: 生日月的特别关怀
  - 流失挽回: 长期未消费的挽回活动

### 7. 客户服务
- **在线客服**:
  - 即时聊天: 与客服的即时聊天功能
  - 常见问题: FAQ常见问题解答
  - 服务评价: 对客服服务的评价
  - 问题反馈: 问题和建议的反馈渠道
- **预约管理**:
  - 预约查询: 查询预约记录和状态
  - 预约变更: 预约时间和服务的变更
  - 预约取消: 预约取消和退款处理
  - 预约提醒: 预约前的提醒通知
- **投诉建议**:
  - 投诉渠道: 服务投诉的提交渠道
  - 处理跟踪: 投诉处理进度的跟踪
  - 满意度调查: 服务满意度调查
  - 改进建议: 服务改进建议收集

### 8. 个人设置
- **账户设置**:
  - 个人信息: 基本信息的修改和完善
  - 手机绑定: 手机号码的绑定和更换
  - 密码设置: 登录密码的设置和修改
  - 账户注销: 账户注销申请和处理
- **消息设置**:
  - 推送通知: 各类推送通知的开关设置
  - 短信通知: 短信通知的订阅设置
  - 邮件通知: 邮件通知的订阅设置
  - 免打扰: 免打扰时间段的设置
- **隐私设置**:
  - 信息公开: 个人信息的公开范围
  - 位置权限: 位置信息的使用权限
  - 数据授权: 数据使用的授权管理
  - 隐私政策: 隐私政策的查看和同意

## 技术实现要点

### 1. 小程序架构设计
```javascript
// app.js - 小程序入口文件
App({
  onLaunch() {
    // 小程序启动时的初始化
    this.initApp()
  },
  
  async initApp() {
    // 检查登录状态
    const token = wx.getStorageSync('token')
    if (token) {
      await this.checkTokenValid(token)
    }
    
    // 获取用户信息
    await this.getUserInfo()
    
    // 初始化全局配置
    this.initGlobalConfig()
  },
  
  globalData: {
    userInfo: null,
    storeInfo: null,
    systemInfo: null
  }
})

// pages/index/index.js - 首页逻辑
Page({
  data: {
    banners: [],
    services: [],
    activities: [],
    quickActions: [
      { id: 1, name: '立即预约', icon: 'calendar', path: '/pages/booking/index' },
      { id: 2, name: '会员充值', icon: 'wallet', path: '/pages/recharge/index' },
      { id: 3, name: '我的预约', icon: 'list', path: '/pages/appointment/list' },
      { id: 4, name: '联系客服', icon: 'service', action: 'contact' }
    ]
  },
  
  onLoad() {
    this.loadHomeData()
  },
  
  async loadHomeData() {
    wx.showLoading({ title: '加载中...' })
    
    try {
      const [banners, services, activities] = await Promise.all([
        this.getBanners(),
        this.getRecommendServices(),
        this.getCurrentActivities()
      ])
      
      this.setData({
        banners,
        services,
        activities
      })
    } catch (error) {
      wx.showToast({ title: '加载失败', icon: 'error' })
    } finally {
      wx.hideLoading()
    }
  },
  
  onQuickAction(e) {
    const action = e.currentTarget.dataset.action
    const path = e.currentTarget.dataset.path
    
    if (path) {
      wx.navigateTo({ url: path })
    } else if (action === 'contact') {
      this.contactService()
    }
  }
})
```

### 2. 在线预约实现
```javascript
// pages/booking/index.js - 预约页面
Page({
  data: {
    selectedService: null,
    selectedTechnician: null,
    selectedDate: '',
    selectedTime: '',
    availableTimes: [],
    totalPrice: 0
  },
  
  onLoad(options) {
    if (options.serviceId) {
      this.loadServiceDetail(options.serviceId)
    }
  },
  
  async loadServiceDetail(serviceId) {
    const service = await this.getServiceDetail(serviceId)
    this.setData({
      selectedService: service,
      totalPrice: service.price
    })
  },
  
  onSelectTechnician(e) {
    const technician = e.currentTarget.dataset.technician
    this.setData({
      selectedTechnician: technician
    })
    
    // 重新加载可用时间
    if (this.data.selectedDate) {
      this.loadAvailableTimes()
    }
  },
  
  onSelectDate(e) {
    const date = e.detail.value
    this.setData({
      selectedDate: date,
      selectedTime: '',
      availableTimes: []
    })
    
    this.loadAvailableTimes()
  },
  
  async loadAvailableTimes() {
    const { selectedService, selectedTechnician, selectedDate } = this.data
    
    if (!selectedService || !selectedDate) return
    
    const times = await this.getAvailableTimes({
      serviceId: selectedService.id,
      technicianId: selectedTechnician?.id,
      date: selectedDate
    })
    
    this.setData({ availableTimes: times })
  },
  
  onSelectTime(e) {
    const time = e.currentTarget.dataset.time
    this.setData({ selectedTime: time })
  },
  
  async confirmBooking() {
    const { selectedService, selectedTechnician, selectedDate, selectedTime } = this.data
    
    if (!selectedService || !selectedDate || !selectedTime) {
      wx.showToast({ title: '请完善预约信息', icon: 'error' })
      return
    }
    
    const bookingData = {
      serviceId: selectedService.id,
      technicianId: selectedTechnician?.id,
      appointmentDate: selectedDate,
      appointmentTime: selectedTime,
      totalPrice: this.data.totalPrice
    }
    
    try {
      const result = await this.createAppointment(bookingData)
      
      wx.showToast({ title: '预约成功', icon: 'success' })
      
      setTimeout(() => {
        wx.navigateTo({
          url: `/pages/appointment/detail?id=${result.appointmentId}`
        })
      }, 1500)
      
    } catch (error) {
      wx.showToast({ title: '预约失败', icon: 'error' })
    }
  }
})
```

### 3. 会员中心实现
```javascript
// pages/member/index.js - 会员中心
Page({
  data: {
    memberInfo: {},
    balanceInfo: {},
    pointsInfo: {},
    privileges: []
  },
  
  onShow() {
    this.loadMemberData()
  },
  
  async loadMemberData() {
    try {
      const [memberInfo, balanceInfo, pointsInfo, privileges] = await Promise.all([
        this.getMemberInfo(),
        this.getBalanceInfo(),
        this.getPointsInfo(),
        this.getMemberPrivileges()
      ])
      
      this.setData({
        memberInfo,
        balanceInfo,
        pointsInfo,
        privileges
      })
    } catch (error) {
      wx.showToast({ title: '加载失败', icon: 'error' })
    }
  },
  
  onRecharge() {
    wx.navigateTo({ url: '/pages/recharge/index' })
  },
  
  onPointsExchange() {
    wx.navigateTo({ url: '/pages/points/exchange' })
  },
  
  onViewPrivileges() {
    wx.navigateTo({ url: '/pages/member/privileges' })
  },
  
  onViewHistory() {
    wx.navigateTo({ url: '/pages/member/history' })
  }
})
```

### 4. 家庭共享实现
```javascript
// pages/family/index.js - 家庭共享
Page({
  data: {
    familyMembers: [],
    sharedBalance: 0,
    isOwner: false
  },
  
  onLoad() {
    this.loadFamilyData()
  },
  
  async loadFamilyData() {
    const familyData = await this.getFamilyInfo()
    this.setData({
      familyMembers: familyData.members,
      sharedBalance: familyData.sharedBalance,
      isOwner: familyData.isOwner
    })
  },
  
  onAddMember() {
    if (!this.data.isOwner) {
      wx.showToast({ title: '只有主账户可以添加成员', icon: 'error' })
      return
    }
    
    wx.navigateTo({ url: '/pages/family/add-member' })
  },
  
  onMemberSetting(e) {
    const memberId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/family/member-setting?memberId=${memberId}`
    })
  },
  
  async removeMember(e) {
    const memberId = e.currentTarget.dataset.id
    
    const result = await wx.showModal({
      title: '确认删除',
      content: '确定要移除该家庭成员吗？'
    })
    
    if (result.confirm) {
      try {
        await this.removeFamilyMember(memberId)
        wx.showToast({ title: '移除成功', icon: 'success' })
        this.loadFamilyData()
      } catch (error) {
        wx.showToast({ title: '移除失败', icon: 'error' })
      }
    }
  }
})
```

## 验收标准

### 功能验收
- [ ] 小程序基础功能完整
- [ ] 在线预约流程顺畅
- [ ] 会员中心功能齐全
- [ ] 家庭共享正常工作
- [ ] 优惠券系统有效
- [ ] 客户服务响应及时

### 用户体验验收
- [ ] 界面美观，操作流畅
- [ ] 加载速度快，响应及时
- [ ] 微信生态集成良好
- [ ] 用户满意度 > 90%
- [ ] 月活跃用户留存率 > 70%

### 性能验收
- [ ] 小程序启动时间 < 3秒
- [ ] 页面切换响应时间 < 1秒
- [ ] 支持10万+用户并发使用
- [ ] 预约成功率 > 98%

## 工作量评估

- **开发工作量**: 22人天
  - 首页与导航: 3人天
  - 在线预约: 6人天
  - 会员中心: 4人天
  - 健康档案: 3人天
  - 家庭共享: 3人天
  - 优惠券活动: 2人天
  - 客户服务: 1人天

- **测试工作量**: 10人天
  - 功能测试: 5人天
  - 用户体验测试: 3人天
  - 性能测试: 2人天

- **总工作量**: 32人天

## 依赖关系

### 前置条件
- [x] T001: 项目架构设计与基础设施搭建
- [x] T002: 用户认证与权限管理系统
- [x] T004: 会员管理与营销系统
- [x] T005: 预约与排班核心系统

### 后置任务
- T010: 数据分析与决策中心
- T012: 第三方集成与API开发

## 风险点和注意事项

### 技术风险
1. **微信审核**: 小程序审核可能不通过
   - **缓解措施**: 严格遵循微信小程序规范

2. **性能限制**: 小程序性能和功能限制
   - **缓解措施**: 优化代码，合理使用缓存

### 业务风险
1. **用户接受度**: 用户可能不习惯使用小程序
   - **缓解措施**: 简化操作流程，提供引导

2. **竞争压力**: 同行业小程序竞争激烈
   - **缓解措施**: 突出特色功能，提升用户体验

## 关键里程碑

1. **第1-2周**: 完成首页、预约和会员中心
2. **第3周**: 完成健康档案和家庭共享
3. **第4周**: 完成优惠券、客服和测试优化

## 交付物

1. **客户小程序**: 完整的微信小程序
2. **管理后台**: 小程序内容管理后台
3. **API接口**: 小程序相关API接口
4. **用户手册**: 客户使用指南
5. **运营手册**: 小程序运营指南
6. **审核材料**: 微信小程序审核所需材料
