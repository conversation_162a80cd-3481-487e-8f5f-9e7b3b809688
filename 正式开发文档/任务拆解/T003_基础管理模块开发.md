# T003 - 基础管理模块开发

## 任务概述

开发门店管理、员工档案、服务商品库、股东股权管理等基础管理功能。这些是系统运营的基础数据管理模块，为业务流程提供主数据支撑，确保连锁企业的标准化运营管理。

## 详细需求描述

### 1. 门店管理
- **门店档案管理**: 创建、编辑、停用门店信息
  - 基本信息: 门店名称、地址、联系方式、营业时间
  - 配置信息: 小票LOGO、门店特色、服务范围
  - 运营信息: 开业时间、经营状态、员工数量
- **门店层级管理**: 支持总部-区域-门店的层级结构
- **门店权限配置**: 不同门店的功能权限和数据访问权限

### 2. 员工档案管理
- **基础信息管理**: 
  - 个人信息: 姓名、性别、年龄、联系方式、身份证
  - 职业信息: 职位、入职时间、工作经验、技能等级
  - 证照管理: 资格证书、健康证、培训证书等附件管理
- **技能标签体系**:
  - 手法技能: 推拿、按摩、拔罐、艾灸等
  - 专业领域: 颈椎、腰椎、肩周、足疗等
  - 技能等级: 初级、中级、高级、专家级
- **员工权限配置**: 基于角色的权限分配

### 3. 服务与商品库管理
- **标准项目库**: 总部建立统一的服务项目标准
  - 项目分类: 按摩类、推拿类、理疗类、养生类
  - 项目属性: 服务时长、技能要求、适用人群
  - 耗材BOM: 每个项目所需的耗材清单和用量
- **门店定价管理**: 门店可基于标准项目进行个性化定价
- **商品管理**: 零售商品和内部耗材的统一管理
- **项目启停控制**: 门店可根据实际情况启用或停用特定项目

### 4. 股东与股权管理
- **股东档案管理**: 
  - 基本信息: 姓名、身份信息、联系方式
  - 投资信息: 出资金额、出资时间、出资方式
  - 股权信息: 持股比例、股权类型、权益说明
- **股权结构管理**: 
  - 支持复杂的股权结构配置
  - 股权变动记录和追溯
  - 股权转让流程管理
- **分红模型配置**:
  - 多种分红模式: 按股权比例、按贡献度、混合模式
  - 分红规则配置: 分红周期、分红条件、分红基数
  - 自动分红计算: 系统自动核算分红金额

### 5. 动态提成方案
- **提成规则配置**:
  - 提成基数: 实收金额、原价金额
  - 提成方式: 固定金额、百分比提成
  - 阶梯提成: 基于业绩阶梯的递增提成
- **多维度提成**:
  - 按角色提成: 不同职位的提成标准
  - 按项目提成: 不同服务项目的提成比例
  - 按客户提成: VIP客户的特殊提成
- **团队激励**: 门店整体业绩奖励机制

### 6. 房间/设备管理
- **资源分类管理**:
  - 普通房间: 基础按摩推拿房间
  - 特殊房间: 艾灸房、汗蒸房、理疗室等
  - 设备资源: 理疗仪、按摩椅等大型设备
- **资源预约管理**: 将房间和设备作为可预约资源
- **资源状态管理**: 可用、占用、维护、停用等状态
- **冲突检测**: 避免资源预约冲突

## 技术实现要点

### 1. 门店管理实现
```java
@Entity
@Table(name = "stores")
public class Store extends BaseEntity {
    private String storeName;
    private String address;
    private String phone;
    private String businessHours;
    private String logoUrl;
    private StoreStatus status;
    private Long parentStoreId; // 支持层级结构
    
    @OneToMany(mappedBy = "store")
    private List<Employee> employees;
}

@Service
public class StoreService {
    
    public StoreDTO createStore(CreateStoreRequest request) {
        // 验证门店信息
        validateStoreInfo(request);
        
        Store store = storeMapper.toEntity(request);
        store.setTenantId(TenantContext.getCurrentTenantId());
        store.setStatus(StoreStatus.ACTIVE);
        
        store = storeRepository.save(store);
        
        // 初始化门店默认配置
        initStoreDefaultConfig(store.getId());
        
        return storeMapper.toDTO(store);
    }
}
```

### 2. 员工技能标签系统
```java
@Entity
public class SkillTag {
    private Long id;
    private String tagName;
    private SkillCategory category; // 手法、领域、等级
    private Integer level; // 技能等级 1-5
    private String description;
}

@Entity
public class EmployeeSkill {
    private Long employeeId;
    private Long skillTagId;
    private Integer proficiencyLevel; // 熟练度 1-10
    private LocalDate certifiedDate; // 认证时间
    private String certificationNo; // 证书编号
}
```

### 3. 股权管理实现
```java
@Entity
public class Shareholder extends BaseEntity {
    private String name;
    private String idCard;
    private String phone;
    private BigDecimal totalInvestment; // 总投资金额
    private BigDecimal currentEquity; // 当前股权比例
}

@Entity
public class EquityRecord {
    private Long shareholderId;
    private BigDecimal amount; // 出资金额
    private BigDecimal equityRatio; // 股权比例
    private LocalDate investmentDate;
    private EquityType type; // 现金、实物、技术等
    private String description;
}

@Service
public class DividendService {
    
    public List<DividendResult> calculateDividend(Long storeId, YearMonth period) {
        // 获取门店当期利润
        BigDecimal profit = financialService.getStoreProfit(storeId, period);
        
        // 获取股东信息
        List<Shareholder> shareholders = shareholderService.getActiveShareholders(storeId);
        
        // 根据配置的分红模型计算分红
        DividendModel model = dividendConfigService.getDividendModel(storeId);
        
        return dividendCalculator.calculate(profit, shareholders, model);
    }
}
```

### 4. 动态提成方案
```java
@Entity
public class CommissionScheme {
    private String schemeName;
    private CommissionType type; // 固定、百分比、阶梯
    private String applicableRoles; // 适用角色
    private String applicableServices; // 适用服务
    private BigDecimal baseRate; // 基础比例
    private String tierConfig; // 阶梯配置JSON
}

@Service
public class CommissionCalculator {
    
    public BigDecimal calculateCommission(Long employeeId, Long orderId) {
        Order order = orderService.getById(orderId);
        Employee employee = employeeService.getById(employeeId);
        
        // 获取适用的提成方案
        CommissionScheme scheme = getApplicableScheme(employee, order);
        
        // 根据方案类型计算提成
        return switch (scheme.getType()) {
            case FIXED -> scheme.getFixedAmount();
            case PERCENTAGE -> order.getActualAmount().multiply(scheme.getBaseRate());
            case TIERED -> calculateTieredCommission(employee, order, scheme);
        };
    }
}
```

## 验收标准

### 功能验收
- [ ] 门店管理功能完整，支持CRUD操作和层级管理
- [ ] 员工档案管理完善，技能标签体系正常工作
- [ ] 服务商品库管理功能齐全，支持门店个性化配置
- [ ] 股东股权管理准确，分红计算无误差
- [ ] 提成方案配置灵活，计算结果准确
- [ ] 房间设备管理完善，预约冲突检测有效

### 业务验收
- [ ] 支持复杂的连锁企业组织架构
- [ ] 技能匹配推荐算法准确率 > 85%
- [ ] 分红计算与财务数据一致性100%
- [ ] 提成计算支持多种复杂场景
- [ ] 资源利用率统计准确

### 性能验收
- [ ] 基础数据查询响应时间 < 200ms
- [ ] 复杂分红计算完成时间 < 5秒
- [ ] 支持10000+员工档案管理
- [ ] 技能匹配查询响应时间 < 300ms

## 工作量评估

- **开发工作量**: 18人天
  - 门店管理: 3人天
  - 员工档案管理: 4人天
  - 服务商品库: 3人天
  - 股东股权管理: 4人天
  - 提成方案: 3人天
  - 房间设备管理: 1人天

- **测试工作量**: 8人天
  - 功能测试: 4人天
  - 业务逻辑测试: 3人天
  - 性能测试: 1人天

- **总工作量**: 26人天

## 依赖关系

### 前置条件
- [x] T001: 项目架构设计与基础设施搭建
- [x] T002: 用户认证与权限管理系统
- [ ] 业务流程和数据模型确认

### 后置任务
- T004: 会员管理与营销系统
- T005: 预约与排班核心系统
- T006: 收银与财务管理系统
- T007: 库存与供应链管理

## 风险点和注意事项

### 业务风险
1. **数据一致性**: 基础数据变更可能影响业务流程
   - **缓解措施**: 严格的数据变更审批流程，变更影响分析

2. **权限复杂性**: 复杂的组织架构可能导致权限混乱
   - **缓解措施**: 清晰的权限继承规则，权限变更日志

### 技术风险
1. **性能问题**: 大量基础数据查询可能影响性能
   - **缓解措施**: 合理的缓存策略，数据库索引优化

2. **数据迁移**: 现有系统数据迁移可能存在问题
   - **缓解措施**: 充分的数据清洗和验证，分批迁移

## 关键里程碑

1. **第1周**: 完成门店和员工管理模块
2. **第2周**: 完成服务商品库管理
3. **第3周**: 完成股东股权管理
4. **第4周**: 完成提成方案和房间设备管理

## 交付物

1. **基础管理服务**: 完整的基础数据管理微服务
2. **管理后台界面**: 基础数据管理的Web界面
3. **数据模型文档**: 详细的数据模型设计文档
4. **业务规则文档**: 提成计算、分红计算等业务规则说明
5. **API接口文档**: 基础数据管理相关API文档
6. **数据迁移工具**: 现有系统数据迁移脚本和工具
