# T008 - 员工移动端应用

## 任务概述

开发员工专用的H5/小程序，包括工作台、业绩查询、假勤管理等功能。为员工提供便捷的移动办公工具，提升工作效率，增强员工体验，实现移动化的人力资源管理。

## 详细需求描述

### 1. 员工工作台
- **个人信息中心**:
  - 个人档案: 基本信息、联系方式、紧急联系人
  - 工作信息: 职位、部门、入职时间、工号
  - 技能标签: 专业技能、认证证书、技能等级
  - 头像设置: 个人头像上传和更换
- **今日工作概览**:
  - 今日排班: 工作时间、休息时间、工作安排
  - 预约安排: 今日预约客户列表和时间安排
  - 待办事项: 需要处理的工作任务
  - 消息通知: 系统通知、工作提醒
- **快捷功能入口**:
  - 打卡签到: 上下班打卡、外出打卡
  - 预约管理: 查看和管理客户预约
  - 业绩查询: 个人业绩和收入查询
  - 请假申请: 在线请假申请和审批

### 2. 排班与考勤管理
- **排班查询**:
  - 个人排班: 查看个人排班安排
  - 班次详情: 工作时间、休息时间、工作内容
  - 排班日历: 月度排班日历视图
  - 换班申请: 与同事换班申请和审批
- **考勤打卡**:
  - 定位打卡: GPS定位确保在店内打卡
  - 拍照打卡: 拍照记录打卡状态
  - 异常打卡: 忘记打卡的补卡申请
  - 外出打卡: 外出工作的打卡记录
- **考勤统计**:
  - 出勤记录: 每日出勤情况统计
  - 迟到早退: 迟到早退次数和时长
  - 加班记录: 加班时间和加班费计算
  - 请假记录: 请假类型、时长、余额

### 3. 业绩与收入管理
- **业绩统计**:
  - 服务业绩: 服务客户数量、服务时长
  - 销售业绩: 商品销售金额、销售数量
  - 业绩排名: 个人在团队中的业绩排名
  - 业绩趋势: 业绩变化趋势图表
- **收入明细**:
  - 基本工资: 基本工资、岗位津贴
  - 提成收入: 服务提成、销售提成
  - 奖金收入: 绩效奖金、团队奖金
  - 收入汇总: 月度、年度收入汇总
- **提成计算**:
  - 提成规则: 查看适用的提成规则
  - 提成明细: 每笔提成的详细计算
  - 提成预估: 当月预估提成收入
  - 提成历史: 历史提成记录查询

### 4. 客户服务管理
- **客户档案查询**:
  - 客户信息: 基本信息、联系方式、偏好
  - 健康档案: 客户健康状况、调理记录
  - 消费记录: 历史消费记录和偏好分析
  - 服务记录: 历史服务记录和评价
- **服务记录管理**:
  - 服务开始: 扫码开始服务，记录开始时间
  - 服务过程: 记录服务内容、使用耗材
  - 服务完成: 记录服务结束时间、客户反馈
  - 服务评价: 客户对服务的评价和建议
- **客户关怀**:
  - 生日提醒: 客户生日提醒和祝福
  - 回访提醒: 服务后回访提醒
  - 健康建议: 基于客户情况的健康建议
  - 预约邀请: 主动邀请客户预约服务

### 5. 学习与培训
- **培训课程**:
  - 在线课程: 专业技能培训课程
  - 视频学习: 技法演示视频学习
  - 考试测评: 在线考试和技能测评
  - 学习记录: 学习进度和成绩记录
- **知识库**:
  - 技法指南: 各种按摩推拿技法指南
  - 健康知识: 中医养生健康知识
  - 产品知识: 服务项目和产品知识
  - 常见问题: 工作中常见问题解答
- **技能认证**:
  - 技能考核: 定期技能考核和认证
  - 证书管理: 各类证书的管理和展示
  - 技能升级: 技能等级提升路径
  - 培训计划: 个人培训计划和安排

### 6. 团队协作
- **内部沟通**:
  - 即时消息: 与同事的即时消息沟通
  - 群组聊天: 部门群组和项目群组
  - 公告通知: 公司公告和重要通知
  - 工作汇报: 日报、周报、月报提交
- **协作工具**:
  - 任务分配: 接收和处理工作任务
  - 文件共享: 工作文件的共享和下载
  - 会议安排: 会议通知和参会确认
  - 投票调研: 参与公司投票和调研
- **互助支持**:
  - 经验分享: 分享工作经验和技巧
  - 问题求助: 向同事求助解决问题
  - 技能交流: 技能学习和交流
  - 团队活动: 团队建设活动参与

### 7. 个人设置
- **账户设置**:
  - 密码修改: 登录密码修改
  - 手机绑定: 手机号码绑定和验证
  - 微信绑定: 微信账号绑定
  - 安全设置: 账户安全设置
- **消息设置**:
  - 推送设置: 消息推送开关设置
  - 提醒设置: 各类提醒的时间设置
  - 免打扰: 免打扰时间段设置
  - 消息分类: 不同类型消息的处理方式
- **隐私设置**:
  - 信息公开: 个人信息公开范围
  - 位置权限: 位置信息使用权限
  - 数据同步: 数据同步和备份设置
  - 退出登录: 安全退出登录

## 技术实现要点

### 1. 移动端架构设计
```javascript
// 使用Vue3 + Vant4构建H5应用
// main.js
import { createApp } from 'vue'
import { ConfigProvider, Locale } from 'vant'
import zhCN from 'vant/es/locale/lang/zh-CN'
import App from './App.vue'
import router from './router'
import store from './store'

const app = createApp(App)

app.use(ConfigProvider)
app.use(Locale, zhCN)
app.use(store)
app.use(router)

app.mount('#app')

// 工作台组件
<template>
  <div class="workspace">
    <van-nav-bar title="工作台" />
    
    <!-- 个人信息卡片 -->
    <van-card
      :thumb="userInfo.avatar"
      :title="userInfo.name"
      :desc="userInfo.position"
    >
      <template #tags>
        <van-tag type="primary">{{ userInfo.level }}</van-tag>
      </template>
    </van-card>
    
    <!-- 今日概览 -->
    <van-cell-group title="今日概览">
      <van-cell title="今日排班" :value="todaySchedule" />
      <van-cell title="预约客户" :value="todayAppointments" />
      <van-cell title="待办事项" :value="todoCount" />
    </van-cell-group>
    
    <!-- 快捷功能 -->
    <van-grid :column-num="4">
      <van-grid-item
        v-for="item in quickActions"
        :key="item.id"
        :icon="item.icon"
        :text="item.text"
        @click="handleQuickAction(item)"
      />
    </van-grid>
  </div>
</template>
```

### 2. 考勤打卡实现
```javascript
// 考勤打卡组件
export default {
  data() {
    return {
      location: null,
      photo: null,
      clockInTime: null
    }
  },
  
  methods: {
    async clockIn() {
      try {
        // 获取GPS定位
        const position = await this.getCurrentPosition()
        
        // 验证位置是否在允许范围内
        const isValidLocation = await this.validateLocation(position)
        if (!isValidLocation) {
          this.$toast('请在店内进行打卡')
          return
        }
        
        // 拍照记录
        const photo = await this.takePhoto()
        
        // 提交打卡记录
        const clockInData = {
          employeeId: this.userInfo.id,
          clockTime: new Date(),
          location: position,
          photo: photo,
          type: 'CLOCK_IN'
        }
        
        await this.submitClockIn(clockInData)
        this.$toast('打卡成功')
        
      } catch (error) {
        this.$toast('打卡失败: ' + error.message)
      }
    },
    
    getCurrentPosition() {
      return new Promise((resolve, reject) => {
        if (!navigator.geolocation) {
          reject(new Error('浏览器不支持定位'))
          return
        }
        
        navigator.geolocation.getCurrentPosition(
          position => resolve(position.coords),
          error => reject(error),
          { enableHighAccuracy: true, timeout: 10000 }
        )
      })
    },
    
    async validateLocation(position) {
      const storeLocation = await this.getStoreLocation()
      const distance = this.calculateDistance(position, storeLocation)
      return distance <= 100 // 100米范围内
    }
  }
}
```

### 3. 业绩统计实现
```javascript
// 业绩统计组件
<template>
  <div class="performance">
    <van-nav-bar title="业绩统计" />
    
    <!-- 时间选择 -->
    <van-cell title="统计周期" @click="showDatePicker = true">
      <template #value>{{ selectedPeriod }}</template>
    </van-cell>
    
    <!-- 业绩概览 -->
    <van-grid :column-num="2">
      <van-grid-item>
        <div class="performance-item">
          <div class="value">{{ performance.serviceCount }}</div>
          <div class="label">服务客户</div>
        </div>
      </van-grid-item>
      <van-grid-item>
        <div class="performance-item">
          <div class="value">¥{{ performance.income }}</div>
          <div class="label">总收入</div>
        </div>
      </van-grid-item>
    </van-grid>
    
    <!-- 业绩图表 -->
    <div class="chart-container">
      <canvas ref="performanceChart"></canvas>
    </div>
    
    <!-- 业绩明细 -->
    <van-list>
      <van-cell
        v-for="item in performanceDetails"
        :key="item.id"
        :title="item.serviceName"
        :value="'¥' + item.amount"
        :label="item.date"
      />
    </van-list>
  </div>
</template>

<script>
import Chart from 'chart.js/auto'

export default {
  data() {
    return {
      selectedPeriod: '本月',
      performance: {},
      performanceDetails: [],
      chart: null
    }
  },
  
  mounted() {
    this.loadPerformanceData()
    this.initChart()
  },
  
  methods: {
    async loadPerformanceData() {
      const response = await this.$api.getEmployeePerformance({
        employeeId: this.userInfo.id,
        period: this.selectedPeriod
      })
      
      this.performance = response.data.summary
      this.performanceDetails = response.data.details
      
      this.updateChart(response.data.chartData)
    },
    
    initChart() {
      const ctx = this.$refs.performanceChart.getContext('2d')
      this.chart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: [],
          datasets: [{
            label: '日收入',
            data: [],
            borderColor: '#1989fa',
            backgroundColor: 'rgba(25, 137, 250, 0.1)'
          }]
        },
        options: {
          responsive: true,
          scales: {
            y: {
              beginAtZero: true
            }
          }
        }
      })
    }
  }
}
</script>
```

### 4. 客户服务管理
```javascript
// 客户服务组件
export default {
  data() {
    return {
      currentService: null,
      serviceStartTime: null,
      usedMaterials: []
    }
  },
  
  methods: {
    async startService(appointmentId) {
      try {
        // 扫码开始服务
        const qrResult = await this.scanQRCode()
        
        // 验证预约信息
        const appointment = await this.validateAppointment(qrResult, appointmentId)
        
        // 开始服务
        this.currentService = {
          appointmentId: appointment.id,
          customerId: appointment.customerId,
          serviceId: appointment.serviceId,
          startTime: new Date()
        }
        
        // 记录服务开始
        await this.$api.startService(this.currentService)
        
        this.$toast('服务已开始')
        
      } catch (error) {
        this.$toast('开始服务失败: ' + error.message)
      }
    },
    
    async completeService() {
      if (!this.currentService) {
        this.$toast('请先开始服务')
        return
      }
      
      const serviceRecord = {
        ...this.currentService,
        endTime: new Date(),
        usedMaterials: this.usedMaterials,
        serviceNotes: this.serviceNotes,
        customerFeedback: this.customerFeedback
      }
      
      await this.$api.completeService(serviceRecord)
      
      // 清空当前服务
      this.currentService = null
      this.usedMaterials = []
      
      this.$toast('服务已完成')
    },
    
    addMaterial(material) {
      this.usedMaterials.push({
        materialId: material.id,
        quantity: material.quantity,
        addTime: new Date()
      })
    }
  }
}
```

## 验收标准

### 功能验收
- [ ] 员工工作台功能完整
- [ ] 考勤打卡准确可靠
- [ ] 业绩统计数据准确
- [ ] 客户服务流程顺畅
- [ ] 学习培训功能完善
- [ ] 团队协作工具有效

### 用户体验验收
- [ ] 界面友好，操作简便
- [ ] 响应速度快，加载流畅
- [ ] 离线功能可用
- [ ] 多设备适配良好
- [ ] 用户满意度 > 85%

### 性能验收
- [ ] 页面加载时间 < 2秒
- [ ] 打卡响应时间 < 3秒
- [ ] 支持1000+员工并发使用
- [ ] 离线数据同步成功率 > 95%

## 工作量评估

- **开发工作量**: 18人天
  - 员工工作台: 3人天
  - 考勤管理: 4人天
  - 业绩管理: 3人天
  - 客户服务: 4人天
  - 学习培训: 2人天
  - 团队协作: 2人天

- **测试工作量**: 8人天
  - 功能测试: 4人天
  - 兼容性测试: 2人天
  - 性能测试: 2人天

- **总工作量**: 26人天

## 依赖关系

### 前置条件
- [x] T001: 项目架构设计与基础设施搭建
- [x] T002: 用户认证与权限管理系统
- [x] T003: 基础管理模块开发
- [x] T005: 预约与排班核心系统

### 后置任务
- T009: 客户小程序端
- T010: 数据分析与决策中心

## 风险点和注意事项

### 技术风险
1. **设备兼容性**: 不同手机设备的兼容性问题
   - **缓解措施**: 充分的设备测试，渐进式增强

2. **网络依赖**: 移动网络不稳定影响使用
   - **缓解措施**: 离线功能，数据缓存

### 用户体验风险
1. **学习成本**: 员工需要学习新的操作方式
   - **缓解措施**: 简化操作流程，提供培训

2. **隐私担忧**: 员工对定位打卡的隐私担忧
   - **缓解措施**: 明确隐私政策，合理使用定位

## 关键里程碑

1. **第1-2周**: 完成工作台和考勤管理
2. **第3周**: 完成业绩和客户服务管理
3. **第4周**: 完成学习培训和团队协作

## 交付物

1. **员工移动端应用**: H5/小程序应用
2. **管理后台**: 员工数据管理后台
3. **API接口**: 移动端相关API接口
4. **用户手册**: 员工使用指南
5. **技术文档**: 移动端技术实现文档
6. **测试报告**: 功能和性能测试报告
