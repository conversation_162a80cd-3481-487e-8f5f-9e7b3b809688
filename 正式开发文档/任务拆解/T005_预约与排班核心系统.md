# T005 - 预约与排班核心系统

## 任务概述

开发可视化预约日历、智能排班系统、冲突检测等核心运营功能。这是按摩推拿行业的核心业务系统，需要处理复杂的资源调度、时间管理和冲突检测，确保门店运营效率最大化。

## 详细需求描述

### 1. 可视化预约日历
- **多维度日历视图**:
  - 日视图: 显示单日详细预约安排
  - 周视图: 显示一周的预约概览
  - 月视图: 显示月度预约统计
  - 技师视图: 按技师维度显示预约安排
- **预约信息展示**:
  - 客户信息: 姓名、联系方式、会员等级
  - 服务信息: 服务项目、时长、价格
  - 资源信息: 技师、房间、设备安排
  - 状态标识: 已预约、进行中、已完成、已取消
- **拖拽操作支持**:
  - 预约时间调整: 拖拽改变预约时间
  - 技师调换: 拖拽分配不同技师
  - 房间调换: 拖拽分配不同房间

### 2. 智能排班系统
- **排班规则配置**:
  - 工作时间: 员工的工作时间段设置
  - 休息安排: 工作日休息、周休、年假等
  - 技能匹配: 根据员工技能自动匹配服务项目
  - 工作负荷: 控制员工每日最大工作量
- **自动排班算法**:
  - 技能优先: 优先安排技能匹配度高的技师
  - 负荷均衡: 平衡各技师的工作负荷
  - 客户偏好: 考虑客户的技师偏好
  - 收益优化: 优化门店整体收益
- **排班冲突检测**:
  - 时间冲突: 检测技师时间安排冲突
  - 资源冲突: 检测房间、设备使用冲突
  - 技能冲突: 检测技师技能不匹配问题
  - 负荷冲突: 检测工作负荷超限问题

### 3. 预约管理功能
- **预约创建流程**:
  - 客户选择: 新客户登记或现有客户选择
  - 服务选择: 服务项目、时长、价格确认
  - 时间选择: 可用时间段展示和选择
  - 资源分配: 自动或手动分配技师和房间
- **预约变更管理**:
  - 改期功能: 支持预约时间调整
  - 换人功能: 支持技师更换
  - 加项功能: 支持服务项目增加
  - 取消功能: 支持预约取消和退款
- **预约提醒系统**:
  - 短信提醒: 预约前24小时、2小时提醒
  - 微信提醒: 小程序消息推送
  - 电话提醒: 重要客户的电话确认
  - 到店提醒: 客户到店后的服务提醒

### 4. 资源管理
- **技师资源管理**:
  - 技师档案: 技能、等级、工作时间
  - 实时状态: 空闲、忙碌、休息、请假
  - 工作负荷: 当日工作量和剩余容量
  - 客户评价: 技师服务评价和反馈
- **房间设备管理**:
  - 房间类型: 普通房、VIP房、特殊功能房
  - 设备配置: 房间内设备和器材清单
  - 使用状态: 空闲、占用、清洁、维护
  - 预约限制: 特殊房间的预约条件

### 5. 等候管理系统
- **排队叫号**:
  - 取号系统: 客户到店取号排队
  - 叫号显示: 大屏显示当前服务号码
  - 等候时间: 预估等候时间显示
  - 优先级管理: VIP客户优先服务
- **等候区管理**:
  - 座位管理: 等候区座位安排
  - 服务提供: 茶水、杂志等服务
  - 环境控制: 音乐、温度、灯光调节
  - 客户安抚: 等候时间过长的客户安抚

### 6. 预约数据分析
- **预约统计分析**:
  - 预约量统计: 日、周、月预约数量趋势
  - 取消率分析: 预约取消原因和趋势
  - 满座率分析: 时间段满座率统计
  - 技师效率: 技师工作效率和客户满意度
- **收益分析**:
  - 时段收益: 不同时间段的收益分析
  - 服务收益: 不同服务项目的收益贡献
  - 技师收益: 技师个人收益贡献分析
  - 预约转化: 预约到实际消费的转化率

## 技术实现要点

### 1. 预约日历实现
```java
@Entity
@Table(name = "appointments")
public class Appointment extends BaseEntity {
    private Long customerId;
    private Long employeeId;
    private Long serviceId;
    private Long roomId;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private AppointmentStatus status;
    private BigDecimal originalPrice;
    private BigDecimal actualPrice;
    private String remarks;
    
    @OneToMany(mappedBy = "appointment")
    private List<AppointmentService> services;
}

@RestController
@RequestMapping("/api/appointments")
public class AppointmentController {
    
    @GetMapping("/calendar")
    public CalendarViewDTO getCalendarView(
            @RequestParam LocalDate startDate,
            @RequestParam LocalDate endDate,
            @RequestParam(required = false) Long employeeId) {
        
        return appointmentService.getCalendarView(startDate, endDate, employeeId);
    }
    
    @PostMapping
    public AppointmentDTO createAppointment(@RequestBody CreateAppointmentRequest request) {
        // 验证预约时间可用性
        validateAppointmentAvailability(request);
        
        // 自动分配资源
        ResourceAllocation allocation = resourceAllocationService.allocateResources(request);
        
        // 创建预约
        return appointmentService.createAppointment(request, allocation);
    }
}
```

### 2. 智能排班算法
```java
@Service
public class IntelligentSchedulingService {
    
    public ScheduleResult generateOptimalSchedule(ScheduleRequest request) {
        // 获取约束条件
        List<ScheduleConstraint> constraints = getScheduleConstraints(request);
        
        // 获取可用资源
        List<Employee> availableEmployees = getAvailableEmployees(request.getDate());
        List<Room> availableRooms = getAvailableRooms(request.getDate());
        
        // 执行排班算法
        return scheduleOptimizer.optimize(constraints, availableEmployees, availableRooms);
    }
    
    private ScheduleResult optimizeSchedule(List<Appointment> appointments, 
                                          List<Employee> employees) {
        // 使用遗传算法或启发式算法优化排班
        GeneticAlgorithm ga = new GeneticAlgorithm();
        ga.setFitnessFunction(new ScheduleFitnessFunction());
        ga.setPopulationSize(100);
        ga.setMaxGenerations(1000);
        
        return ga.evolve(appointments, employees);
    }
}

@Component
public class ConflictDetector {
    
    public List<ScheduleConflict> detectConflicts(List<Appointment> appointments) {
        List<ScheduleConflict> conflicts = new ArrayList<>();
        
        // 检测时间冲突
        conflicts.addAll(detectTimeConflicts(appointments));
        
        // 检测资源冲突
        conflicts.addAll(detectResourceConflicts(appointments));
        
        // 检测技能冲突
        conflicts.addAll(detectSkillConflicts(appointments));
        
        return conflicts;
    }
}
```

### 3. 实时状态管理
```java
@Component
public class ResourceStatusManager {
    
    private final RedisTemplate<String, Object> redisTemplate;
    
    public void updateEmployeeStatus(Long employeeId, EmployeeStatus status) {
        String key = "employee:status:" + employeeId;
        EmployeeStatusInfo statusInfo = EmployeeStatusInfo.builder()
            .employeeId(employeeId)
            .status(status)
            .updateTime(LocalDateTime.now())
            .build();
            
        redisTemplate.opsForValue().set(key, statusInfo, Duration.ofHours(24));
        
        // 发布状态变更事件
        eventPublisher.publishEvent(new EmployeeStatusChangedEvent(employeeId, status));
    }
    
    public List<EmployeeStatusInfo> getAvailableEmployees(LocalDateTime startTime, 
                                                         LocalDateTime endTime) {
        // 查询指定时间段内可用的技师
        return employeeRepository.findAvailableEmployees(startTime, endTime);
    }
}

@EventListener
public class AppointmentEventHandler {
    
    @Async
    public void handleAppointmentCreated(AppointmentCreatedEvent event) {
        // 发送预约确认通知
        notificationService.sendAppointmentConfirmation(event.getAppointment());
        
        // 更新资源状态
        resourceStatusManager.updateResourceStatus(event.getAppointment());
        
        // 触发相关业务流程
        businessProcessService.triggerAppointmentWorkflow(event.getAppointment());
    }
}
```

### 4. 预约提醒系统
```java
@Component
public class AppointmentReminderService {
    
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void sendAppointmentReminders() {
        LocalDateTime now = LocalDateTime.now();
        
        // 24小时提醒
        List<Appointment> appointments24h = appointmentRepository
            .findAppointmentsInTimeRange(now.plusHours(23), now.plusHours(25));
        appointments24h.forEach(this::send24HourReminder);
        
        // 2小时提醒
        List<Appointment> appointments2h = appointmentRepository
            .findAppointmentsInTimeRange(now.plusHours(1), now.plusHours(3));
        appointments2h.forEach(this::send2HourReminder);
    }
    
    private void send24HourReminder(Appointment appointment) {
        Customer customer = customerService.getById(appointment.getCustomerId());
        
        // 发送短信提醒
        smsService.sendAppointmentReminder(customer.getPhone(), appointment, "24小时");
        
        // 发送微信提醒
        wechatService.sendAppointmentReminder(customer.getWechatOpenId(), appointment);
    }
}
```

## 验收标准

### 功能验收
- [ ] 预约日历多视图正常显示和操作
- [ ] 智能排班算法能够生成合理的排班方案
- [ ] 冲突检测准确识别各类冲突
- [ ] 预约创建、变更、取消流程完整
- [ ] 资源状态实时更新和查询
- [ ] 预约提醒按时准确发送
- [ ] 等候管理系统正常工作

### 业务验收
- [ ] 排班效率提升 > 20%
- [ ] 资源利用率 > 85%
- [ ] 预约冲突率 < 2%
- [ ] 客户满意度 > 90%
- [ ] 预约取消率 < 10%

### 性能验收
- [ ] 日历视图加载时间 < 1秒
- [ ] 排班算法执行时间 < 30秒
- [ ] 冲突检测响应时间 < 500ms
- [ ] 支持1000+并发预约操作
- [ ] 实时状态更新延迟 < 3秒

## 工作量评估

- **开发工作量**: 25人天
  - 预约日历: 6人天
  - 智能排班: 8人天
  - 冲突检测: 3人天
  - 预约管理: 4人天
  - 资源管理: 2人天
  - 等候管理: 2人天

- **测试工作量**: 12人天
  - 功能测试: 6人天
  - 算法测试: 3人天
  - 性能测试: 3人天

- **总工作量**: 37人天

## 依赖关系

### 前置条件
- [x] T001: 项目架构设计与基础设施搭建
- [x] T002: 用户认证与权限管理系统
- [x] T003: 基础管理模块开发
- [x] T004: 会员管理与营销系统

### 后置任务
- T006: 收银与财务管理系统
- T008: 员工移动端应用
- T009: 客户小程序端
- T011: AI智能化功能模块

## 风险点和注意事项

### 技术风险
1. **算法复杂性**: 智能排班算法可能过于复杂
   - **缓解措施**: 分阶段实现，先简单后复杂

2. **性能问题**: 大量预约数据可能影响查询性能
   - **缓解措施**: 数据分片、缓存优化

### 业务风险
1. **用户接受度**: 新的排班方式可能需要适应期
   - **缓解措施**: 充分培训，渐进式推广

2. **数据准确性**: 预约数据错误可能影响业务
   - **缓解措施**: 多重验证，数据备份

## 关键里程碑

1. **第1-2周**: 完成预约日历和基础预约管理
2. **第3-4周**: 完成智能排班和冲突检测
3. **第5周**: 完成资源管理和等候管理
4. **第6周**: 完成测试和优化

## 交付物

1. **预约管理服务**: 完整的预约管理微服务
2. **排班算法引擎**: 智能排班算法服务
3. **预约管理界面**: Web端预约管理界面
4. **移动端预约**: 员工和客户移动端预约功能
5. **算法文档**: 排班算法设计和优化文档
6. **操作手册**: 预约管理操作指南
