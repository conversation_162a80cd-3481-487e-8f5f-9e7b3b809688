# T006 - 收银与财务管理系统

## 任务概述

实现POS收银台、混合支付、财务对账、成本管理等财务相关功能。这是门店运营的核心财务系统，需要确保资金安全、账目准确、合规操作，支持复杂的支付场景和精确的财务核算。

## 详细需求描述

### 1. POS收银台系统
- **收银界面设计**:
  - 商品/服务快速选择: 常用项目快捷按钮
  - 购物车管理: 添加、删除、修改商品和服务
  - 客户信息: 会员识别、储值卡余额显示
  - 优惠应用: 折扣、优惠券、积分抵扣
- **收银流程管理**:
  - 订单创建: 服务项目、商品、技师分配
  - 价格计算: 原价、折扣、最终价格计算
  - 支付处理: 多种支付方式组合支付
  - 小票打印: 消费小票、发票打印
- **收银员权限控制**:
  - 操作权限: 折扣权限、退款权限、修改权限
  - 审批流程: 超权限操作需要主管审批
  - 操作日志: 详细记录所有收银操作

### 2. 混合支付系统
- **支付方式支持**:
  - 现金支付: 现金收款、找零计算
  - 刷卡支付: 银行卡、信用卡支付
  - 移动支付: 微信支付、支付宝、云闪付
  - 储值卡支付: 会员储值卡余额扣减
  - 积分支付: 积分抵扣部分金额
- **组合支付处理**:
  - 多种支付方式组合: 如储值卡+微信支付
  - 支付优先级: 自动选择最优支付组合
  - 支付限额: 不同支付方式的限额控制
  - 支付失败处理: 支付失败的回滚机制
- **支付安全保障**:
  - 支付加密: 支付数据传输加密
  - 防重复支付: 避免重复扣款
  - 支付验证: 支付结果验证和确认
  - 异常处理: 支付异常的处理流程

### 3. 财务对账系统
- **日结对账**:
  - 收银汇总: 各收银员的收银汇总
  - 支付方式汇总: 各支付方式的收款汇总
  - 现金盘点: 现金收入与实际现金对比
  - 差异分析: 账实差异的原因分析
- **第三方对账**:
  - 微信对账: 与微信支付平台对账
  - 支付宝对账: 与支付宝平台对账
  - 银行对账: 与银行流水对账
  - 自动对账: 系统自动对账和差异提醒
- **财务报表**:
  - 日报表: 每日收入、支出、利润报表
  - 月报表: 月度财务汇总报表
  - 年报表: 年度财务分析报表
  - 自定义报表: 按需生成各类财务报表

### 4. 成本管理系统
- **成本分类管理**:
  - 直接成本: 人工成本、耗材成本
  - 间接成本: 房租、水电、设备折旧
  - 变动成本: 随业务量变化的成本
  - 固定成本: 不随业务量变化的成本
- **成本核算**:
  - 服务成本: 每项服务的成本核算
  - 门店成本: 门店整体成本分析
  - 技师成本: 技师个人成本核算
  - 时段成本: 不同时段的成本分析
- **盈利分析**:
  - 毛利率分析: 各项服务的毛利率
  - 净利率分析: 扣除所有成本后的净利率
  - 盈亏平衡: 盈亏平衡点分析
  - 投资回报: ROI和投资回报周期

### 5. 发票管理系统
- **发票类型支持**:
  - 普通发票: 纸质普通发票
  - 电子发票: 电子普通发票
  - 专用发票: 增值税专用发票(企业客户)
  - 定额发票: 小额消费定额发票
- **发票开具流程**:
  - 客户信息: 发票抬头、税号等信息
  - 发票内容: 服务项目、金额明细
  - 发票验证: 发票信息验证和确认
  - 发票发送: 电子发票邮件/短信发送
- **发票管理**:
  - 发票库存: 纸质发票库存管理
  - 发票作废: 错误发票的作废处理
  - 发票查询: 历史发票查询和重打
  - 税务申报: 发票数据导出用于税务申报

### 6. 资金安全管理
- **现金管理**:
  - 现金限额: 收银台现金存放限额
  - 现金交接: 班次交接现金清点
  - 现金存取: 现金存入保险柜流程
  - 现金监控: 现金异常情况监控
- **账户安全**:
  - 权限控制: 财务操作权限严格控制
  - 操作审计: 所有财务操作详细记录
  - 异常监控: 异常交易实时监控报警
  - 数据备份: 财务数据定期备份

## 技术实现要点

### 1. POS收银系统实现
```java
@Entity
@Table(name = "orders")
public class Order extends BaseEntity {
    private String orderNo;
    private Long customerId;
    private Long cashierId;
    private BigDecimal originalAmount;
    private BigDecimal discountAmount;
    private BigDecimal actualAmount;
    private OrderStatus status;
    private LocalDateTime paymentTime;
    
    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL)
    private List<OrderItem> orderItems;
    
    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL)
    private List<Payment> payments;
}

@RestController
@RequestMapping("/api/cashier")
public class CashierController {
    
    @PostMapping("/orders")
    public OrderDTO createOrder(@RequestBody CreateOrderRequest request) {
        // 验证商品和服务
        validateOrderItems(request.getItems());
        
        // 计算价格
        PriceCalculationResult priceResult = priceCalculationService.calculate(request);
        
        // 创建订单
        Order order = orderService.createOrder(request, priceResult);
        
        return orderMapper.toDTO(order);
    }
    
    @PostMapping("/orders/{orderId}/pay")
    public PaymentResultDTO processPayment(@PathVariable Long orderId, 
                                         @RequestBody PaymentRequest request) {
        // 验证支付金额
        validatePaymentAmount(orderId, request.getAmount());
        
        // 处理混合支付
        return paymentService.processHybridPayment(orderId, request);
    }
}
```

### 2. 混合支付处理
```java
@Service
public class HybridPaymentService {
    
    public PaymentResult processHybridPayment(Long orderId, List<PaymentMethod> methods) {
        Order order = orderService.getById(orderId);
        BigDecimal totalAmount = order.getActualAmount();
        BigDecimal paidAmount = BigDecimal.ZERO;
        
        List<PaymentRecord> paymentRecords = new ArrayList<>();
        
        // 按优先级处理各种支付方式
        for (PaymentMethod method : methods) {
            if (paidAmount.compareTo(totalAmount) >= 0) {
                break;
            }
            
            BigDecimal remainingAmount = totalAmount.subtract(paidAmount);
            BigDecimal paymentAmount = method.getAmount().min(remainingAmount);
            
            PaymentRecord record = processPaymentMethod(method, paymentAmount);
            if (record.isSuccess()) {
                paymentRecords.add(record);
                paidAmount = paidAmount.add(paymentAmount);
            }
        }
        
        // 检查支付是否完成
        if (paidAmount.compareTo(totalAmount) >= 0) {
            order.setStatus(OrderStatus.PAID);
            orderService.save(order);
            
            // 发送支付成功通知
            notificationService.sendPaymentSuccessNotification(order);
        }
        
        return PaymentResult.builder()
            .orderId(orderId)
            .totalAmount(totalAmount)
            .paidAmount(paidAmount)
            .paymentRecords(paymentRecords)
            .success(paidAmount.compareTo(totalAmount) >= 0)
            .build();
    }
}

@Component
public class PaymentMethodProcessor {
    
    public PaymentRecord processWechatPay(BigDecimal amount, String openId) {
        try {
            WechatPayRequest request = WechatPayRequest.builder()
                .amount(amount)
                .openId(openId)
                .build();
                
            WechatPayResponse response = wechatPayClient.pay(request);
            
            return PaymentRecord.builder()
                .paymentMethod(PaymentMethodType.WECHAT)
                .amount(amount)
                .transactionId(response.getTransactionId())
                .success(response.isSuccess())
                .build();
        } catch (Exception e) {
            log.error("微信支付失败", e);
            return PaymentRecord.failed(PaymentMethodType.WECHAT, amount);
        }
    }
}
```

### 3. 财务对账实现
```java
@Service
public class ReconciliationService {
    
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void dailyReconciliation() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        
        // 系统内部对账
        InternalReconciliationResult internalResult = performInternalReconciliation(yesterday);
        
        // 第三方平台对账
        ThirdPartyReconciliationResult thirdPartyResult = performThirdPartyReconciliation(yesterday);
        
        // 生成对账报告
        ReconciliationReport report = generateReconciliationReport(internalResult, thirdPartyResult);
        
        // 发送对账结果通知
        notificationService.sendReconciliationReport(report);
    }
    
    private InternalReconciliationResult performInternalReconciliation(LocalDate date) {
        // 获取当日所有订单
        List<Order> orders = orderRepository.findByDate(date);
        
        // 按支付方式汇总
        Map<PaymentMethodType, BigDecimal> paymentSummary = orders.stream()
            .flatMap(order -> order.getPayments().stream())
            .collect(Collectors.groupingBy(
                Payment::getPaymentMethod,
                Collectors.reducing(BigDecimal.ZERO, Payment::getAmount, BigDecimal::add)
            ));
        
        // 现金盘点对比
        BigDecimal systemCashAmount = paymentSummary.get(PaymentMethodType.CASH);
        BigDecimal actualCashAmount = cashCountService.getActualCashAmount(date);
        BigDecimal cashDifference = systemCashAmount.subtract(actualCashAmount);
        
        return InternalReconciliationResult.builder()
            .date(date)
            .paymentSummary(paymentSummary)
            .cashDifference(cashDifference)
            .build();
    }
}
```

### 4. 成本核算系统
```java
@Service
public class CostCalculationService {
    
    public ServiceCostResult calculateServiceCost(Long serviceId, LocalDate date) {
        Service service = serviceRepository.findById(serviceId);
        
        // 计算直接成本
        BigDecimal directCost = calculateDirectCost(service, date);
        
        // 计算间接成本
        BigDecimal indirectCost = calculateIndirectCost(service, date);
        
        // 计算总成本
        BigDecimal totalCost = directCost.add(indirectCost);
        
        // 计算毛利率
        BigDecimal revenue = getServiceRevenue(serviceId, date);
        BigDecimal grossProfit = revenue.subtract(totalCost);
        BigDecimal grossMargin = grossProfit.divide(revenue, 4, RoundingMode.HALF_UP);
        
        return ServiceCostResult.builder()
            .serviceId(serviceId)
            .date(date)
            .directCost(directCost)
            .indirectCost(indirectCost)
            .totalCost(totalCost)
            .revenue(revenue)
            .grossProfit(grossProfit)
            .grossMargin(grossMargin)
            .build();
    }
    
    private BigDecimal calculateDirectCost(Service service, LocalDate date) {
        // 人工成本
        BigDecimal laborCost = calculateLaborCost(service, date);
        
        // 耗材成本
        BigDecimal materialCost = calculateMaterialCost(service, date);
        
        return laborCost.add(materialCost);
    }
}
```

## 验收标准

### 功能验收
- [ ] POS收银台完整功能正常工作
- [ ] 混合支付各种组合场景正确处理
- [ ] 财务对账自动执行且结果准确
- [ ] 成本核算计算准确无误差
- [ ] 发票开具和管理功能完善
- [ ] 资金安全管控措施有效

### 财务验收
- [ ] 账目准确性100%，无资金差错
- [ ] 对账差异率 < 0.1%
- [ ] 支付成功率 > 99.5%
- [ ] 财务报表数据准确性100%
- [ ] 成本核算误差 < 0.01%

### 性能验收
- [ ] 收银操作响应时间 < 2秒
- [ ] 支付处理响应时间 < 5秒
- [ ] 对账处理完成时间 < 30分钟
- [ ] 支持100+并发收银操作
- [ ] 财务报表生成时间 < 10秒

## 工作量评估

- **开发工作量**: 28人天
  - POS收银系统: 8人天
  - 混合支付系统: 6人天
  - 财务对账: 5人天
  - 成本管理: 4人天
  - 发票管理: 3人天
  - 资金安全: 2人天

- **测试工作量**: 15人天
  - 功能测试: 8人天
  - 财务准确性测试: 4人天
  - 性能测试: 3人天

- **总工作量**: 43人天

## 依赖关系

### 前置条件
- [x] T001: 项目架构设计与基础设施搭建
- [x] T002: 用户认证与权限管理系统
- [x] T003: 基础管理模块开发
- [x] T004: 会员管理与营销系统
- [x] T005: 预约与排班核心系统

### 后置任务
- T010: 数据分析与决策中心
- T012: 第三方集成与API开发

## 风险点和注意事项

### 财务风险
1. **资金安全**: 支付数据和资金安全是最高优先级
   - **缓解措施**: 多重加密、权限控制、操作审计

2. **账目准确性**: 财务数据必须100%准确
   - **缓解措施**: 多重验证、自动对账、人工复核

### 技术风险
1. **支付失败**: 第三方支付可能出现故障
   - **缓解措施**: 多支付通道、失败重试、降级方案

2. **数据一致性**: 分布式环境下的数据一致性
   - **缓解措施**: 分布式事务、最终一致性保证

## 关键里程碑

1. **第1-2周**: 完成POS收银和混合支付
2. **第3-4周**: 完成财务对账和成本管理
3. **第5周**: 完成发票管理和资金安全
4. **第6周**: 完成测试和上线准备

## 交付物

1. **收银管理服务**: 完整的收银财务微服务
2. **POS收银界面**: 收银员操作界面
3. **财务管理后台**: 财务人员管理界面
4. **财务报表系统**: 各类财务报表和分析
5. **对账工具**: 自动对账和差异分析工具
6. **财务操作手册**: 详细的财务操作指南
