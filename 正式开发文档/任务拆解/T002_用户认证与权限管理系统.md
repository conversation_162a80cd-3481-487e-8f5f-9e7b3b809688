# T002 - 用户认证与权限管理系统

## 任务概述

实现多角色用户认证、RBAC权限控制、多租户数据隔离等核心安全功能。这是系统安全的基石，确保不同角色用户只能访问其权限范围内的功能和数据，同时保证多租户之间的数据完全隔离。

## 详细需求描述

### 1. 多角色用户认证
- **六端用户角色**:
  - 总部管理层/股东: 全局管控权限
  - 门店店长: 门店运营管理权限
  - 员工(技师/助理): 个人工作相关权限
  - 收银员: 收银和基础客户管理权限
  - 顾客(会员): 个人账户和预约权限
  - 系统管理员: 系统配置和维护权限

- **认证方式**:
  - 手机号+验证码登录
  - 微信授权登录(小程序端)
  - 账号密码登录(管理端)
  - JWT Token认证机制

### 2. RBAC权限控制模型
- **角色权限矩阵**: 精细化权限控制，支持功能权限和数据权限
- **动态权限配置**: 支持运行时权限调整
- **权限继承**: 支持角色权限继承关系
- **临时授权**: 支持临时权限授予和回收

### 3. 多租户数据隔离
- **租户识别**: 基于域名、Token等方式识别租户
- **数据隔离**: 所有业务数据按租户ID隔离
- **资源隔离**: 不同租户的资源完全隔离
- **跨租户访问控制**: 严格防止数据泄露

### 4. 安全增强功能
- **密码策略**: 强密码要求、定期更换提醒
- **登录安全**: 异地登录提醒、多设备管理
- **操作审计**: 敏感操作日志记录
- **会话管理**: 会话超时、强制下线

## 技术实现要点

### 1. JWT认证实现
```java
@Service
public class JwtTokenService {
    
    public String generateToken(UserDetails userDetails, String tenantId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("tenantId", tenantId);
        claims.put("roles", userDetails.getAuthorities());
        
        return Jwts.builder()
            .setClaims(claims)
            .setSubject(userDetails.getUsername())
            .setIssuedAt(new Date())
            .setExpiration(new Date(System.currentTimeMillis() + JWT_EXPIRATION))
            .signWith(SignatureAlgorithm.HS512, JWT_SECRET)
            .compact();
    }
}
```

### 2. RBAC权限模型
```java
@Entity
public class Role {
    private Long id;
    private String roleName;
    private String description;
    private Long tenantId;
    
    @ManyToMany
    private Set<Permission> permissions;
}

@Entity
public class Permission {
    private Long id;
    private String resource;      // 资源标识
    private String action;        // 操作类型: CREATE, READ, UPDATE, DELETE
    private String condition;     // 权限条件: 如只能访问自己的数据
}
```

### 3. 多租户上下文管理
```java
@Component
public class TenantContext {
    private static final ThreadLocal<String> TENANT_ID = new ThreadLocal<>();
    
    public static void setCurrentTenantId(String tenantId) {
        TENANT_ID.set(tenantId);
    }
    
    public static String getCurrentTenantId() {
        return TENANT_ID.get();
    }
    
    public static void clear() {
        TENANT_ID.remove();
    }
}

@Component
public class TenantFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        try {
            String tenantId = extractTenantId((HttpServletRequest) request);
            TenantContext.setCurrentTenantId(tenantId);
            chain.doFilter(request, response);
        } finally {
            TenantContext.clear();
        }
    }
}
```

### 4. 权限验证注解
```java
@PreAuthorize("hasPermission(#customerId, 'CUSTOMER', 'READ')")
public CustomerDTO getCustomer(Long customerId) {
    return customerService.findById(customerId);
}

@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {
    
    @Override
    public boolean hasPermission(Authentication auth, Object targetId, Object permission) {
        // 实现具体的权限验证逻辑
        return permissionService.hasPermission(
            auth.getName(), 
            targetId.toString(), 
            permission.toString()
        );
    }
}
```

## 验收标准

### 功能验收
- [ ] 六种角色用户可以正常登录认证
- [ ] JWT Token生成和验证正常工作
- [ ] RBAC权限控制精确生效，无越权访问
- [ ] 多租户数据完全隔离，无数据泄露
- [ ] 权限配置界面可以动态调整权限
- [ ] 操作审计日志完整记录敏感操作

### 安全验收
- [ ] 密码加密存储，使用BCrypt算法
- [ ] JWT Token包含必要的安全信息且不可伪造
- [ ] 会话超时机制正常工作
- [ ] 异地登录检测和提醒功能正常
- [ ] SQL注入、XSS等常见攻击防护有效

### 性能验收
- [ ] 用户登录响应时间 < 500ms
- [ ] 权限验证响应时间 < 100ms
- [ ] 支持1000并发用户同时在线
- [ ] 租户上下文切换开销 < 10ms

## 工作量评估

- **开发工作量**: 12人天
  - 用户认证模块: 3人天
  - RBAC权限模型: 4人天
  - 多租户隔离: 3人天
  - 安全增强功能: 2人天

- **测试工作量**: 6人天
  - 功能测试: 2人天
  - 安全测试: 3人天
  - 性能测试: 1人天

- **总工作量**: 18人天

## 依赖关系

### 前置条件
- [x] T001: 项目架构设计与基础设施搭建
- [ ] 用户角色和权限需求确认
- [ ] 安全合规要求明确

### 后置任务
- T003: 基础管理模块开发
- T004: 会员管理与营销系统
- T005: 预约与排班核心系统
- T006: 收银与财务管理系统

## 风险点和注意事项

### 安全风险
1. **权限绕过**: 复杂的权限逻辑可能存在绕过漏洞
   - **缓解措施**: 充分的安全测试，代码审计，最小权限原则

2. **数据泄露**: 多租户隔离不当可能导致数据泄露
   - **缓解措施**: 严格的数据访问控制，定期安全审计

3. **会话劫持**: JWT Token可能被恶意获取
   - **缓解措施**: HTTPS传输，Token短期有效，刷新机制

### 技术风险
1. **性能影响**: 权限验证可能影响系统性能
   - **缓解措施**: 权限缓存，异步验证，批量权限检查

2. **复杂性**: RBAC模型过于复杂可能影响维护
   - **缓解措施**: 合理的权限粒度设计，清晰的权限文档

### 业务风险
1. **用户体验**: 过于严格的权限控制可能影响用户体验
   - **缓解措施**: 合理的权限设计，友好的错误提示

2. **合规要求**: 健康数据访问需要满足法规要求
   - **缓解措施**: 严格按照GDPR等法规设计权限控制

## 关键里程碑

1. **第1周**: 完成用户认证和JWT实现
2. **第2周**: 完成RBAC权限模型设计和实现
3. **第3周**: 完成多租户数据隔离机制
4. **第4周**: 完成安全增强功能和测试

## 交付物

1. **用户认证服务**: 完整的用户认证微服务
2. **权限管理服务**: RBAC权限控制服务
3. **权限配置界面**: 管理员权限配置后台
4. **安全框架文档**: 详细的安全设计和使用说明
5. **权限测试报告**: 完整的权限验证测试报告
6. **安全审计工具**: 权限和操作审计功能
