# 按摩推拿连锁管理系统 - 系统设计说明书 (SDD)

**版本**: 1.1
**日期**: 2024-07-26
**关联PRD版本**: 1.1

## 变更历史

| 版本 | 日期 | 修改人 | 变更描述 |
| :--- | :--- | :--- | :--- |
| 1.0 | 2024-07-25 | 系统架构师 | 初始版本 |
| 1.1 | 2024-07-26 | 系统架构师 | 新增“数据迁移策略”章节。 |

---

## 1. 系统概述

本系统设计说明书 (System Design Document, SDD) 旨在根据《产品需求文档 (PRD)》V1.0，提供“按摩推拿连锁管理系统”的全面技术设计方案。本文档将详细阐述系统的总体架构、技术选型、模块划分、部署策略及关键技术实现思路，作为后续开发、测试和运维工作的核心技术指导文件。

### 1.1 设计目标

- **高可用性**: 确保系统99.9%以上的可用性，关键业务无单点故障。
- **高性能**: 核心接口响应时间低于500ms，支持高并发访问。
- **高扩展性**: 采用松耦合设计，支持业务功能的快速迭代和未来SaaS化扩展。
- **安全性**: 建立多层次安全防护体系，确保用户数据，特别是健康敏感信息的安全。
- **易维护性**: 模块化设计，代码结构清晰，有完善的日志和监控体系。

### 1.2 总体架构：六端一体化微服务架构

系统将采用**前后端分离**的**微服务架构**，构建一个支持多终端的**“六端一体化”**平台。

- **六端入口**:
    1.  **顾客端**: 微信小程序，提供自助预约、账户管理等功能。
    2.  **员工端**: H5/小程序，为技师/助理提供移动工作台。
    3.  **收银端**: PC Web/桌面应用，适配收银机和扫码设备。
    4.  **店长端**: H5/小程序，提供移动化的门店管理驾驶舱。
    5.  **股东端**: H5/小程序，提供投资回报与分红查询。
    6.  **总部端**: PC Web，功能最全面的后台管理系统。

- **架构图**:
  ```mermaid
  graph TD
      subgraph 用户终端
          A[顾客端小程序]
          B[员工端H5/小程序]
          C[收银端PC/App]
          D[店长端H5/小程序]
          E[股东端H5/小程序]
          F[总部端PC Web]
      end

      subgraph 基础设施与中间件
          G[API网关]
          H[负载均衡]
          I[消息队列 MQ]
          J[分布式缓存 Redis]
          K[搜索引擎 ES]
          L[对象存储 OSS]
      end

      subgraph 后端微服务
          S1[用户与权限服务]
          S2[会员与营销服务]
          S3[预约与排班服务]
          S4[订单与支付服务]
          S5[库存与供应链服务]
          S6[数据与报表服务]
          S7[AI与算法服务]
          S8[通知与消息服务]
      end

      subgraph 数据存储
          DB1[业务主库 MySQL集群]
          DB2[数据仓库 ClickHouse/Doris]
      end

      subgraph 运维监控
          M1[配置中心 Nacos]
          M2[服务注册与发现 Nacos]
          M3[分布式链路追踪 SkyWalking]
          M4[日志系统 ELK]
          M5[监控告警 Prometheus + Grafana]
      end

      A & B & C & D & E & F --> H --> G

      G --> S1 & S2 & S3 & S4 & S5 & S6 & S7 & S8

      S1 & S2 & S3 & S4 & S5 & S6 & S7 & S8 --> I
      S1 & S2 & S3 & S4 & S5 & S6 & S7 & S8 --> J
      S1 & S2 & S3 & S4 & S5 & S6 & S7 & S8 --> K
      S1 & S2 & S3 & S4 & S5 & S6 & S7 & S8 --> L
      S1 & S2 & S3 & S4 & S5 & S6 & S7 & S8 --> DB1
      
      DB1 -- 数据同步/ETL --> DB2
      S6 --> DB2
      
      S1 & S2 & S3 & S4 & S5 & S6 & S7 & S8 -- 注册/配置 --> M1 & M2
      S1 & S2 & S3 & S4 & S5 & S6 & S7 & S8 -- 监控数据 --> M3 & M4 & M5
  ```

---

## 2. 技术选型

| 领域 | 技术栈 | 选型理由 |
| :--- | :--- | :--- |
| **后端** | Java (Spring Boot & Spring Cloud Alibaba) | 生态成熟，社区活跃，稳定性高，微服务全家桶方案完善，适合构建复杂的企业级应用。 |
| **前端** | Vue 3 + TypeScript (PC端), Uni-app (移动端) | PC端采用Vue 3保证开发效率和性能。移动端采用Uni-app一套代码可编译成小程序、H5等多端，极大提升开发效率。 |
| **数据库** | MySQL 8.0 (主库), Redis (缓存), ClickHouse (数据仓库) | MySQL作为主库稳定可靠。Redis处理高并发缓存。ClickHouse用于OLAP场景，满足复杂报表和数据分析的高性能查询需求。 |
| **消息队列** | RabbitMQ / Kafka | RabbitMQ适用于业务解耦和异步通信。若未来日志量和数据量巨大，可考虑引入Kafka。 |
| **搜索引擎** | Elasticsearch | 提供强大的全文检索和聚合分析能力，用于项目库搜索、知识库检索等场景。 |
| **AI/算法** | Python (Flask/FastAPI) + Scikit-learn/TensorFlow | Python在AI领域拥有最丰富的库和生态。通过独立的AI服务提供算法能力，与Java主业务系统通过API解耦。 |
| **容器化** | Docker + Kubernetes (K8s) | 实现应用的标准化打包、部署和弹性伸缩，简化运维管理。 |
| **配置中心** | Nacos | 统一管理所有微服务的配置，支持动态刷新，同时提供服务注册与发现功能。 |

---

## 3. 模块设计原则与职责划分

### 3.1 微服务拆分原则
- **单一职责**: 每个服务聚焦于一个独立的业务领域。
- **高内聚，低耦合**: 服务内部功能紧密相关，服务之间通过标准化API通信，减少直接依赖。
- **独立部署**: 每个服务都可以独立开发、测试、部署和扩展。
- **数据库分离**: 每个服务原则上拥有自己的独立数据库或Schema，避免跨库操作。

### 3.2 核心服务职责

| 服务名称 | 核心职责 | 关联模块 (PRD) |
| :--- | :--- | :--- |
| **用户与权限服务** | 负责所有用户（顾客、员工、股东）的身份认证、信息管理、角色权限(RBAC)控制、多租户隔离。 | 基础管理、员工端 |
| **会员与营销服务** | 管理会员等级、积分、储值、优惠券、营销活动、转介绍、MA自动化规则引擎。 | 会员运营与营销 |
| **预约与排班服务** | 核心业务调度中心。处理可视化预约、智能排班、资源（房间/设备）管理与冲突检测。 | 核心运营 |
| **订单与支付服务** | 负责收银开单、订单生命周期管理、集成第三方支付、处理退款、财务对账。 | 收银与财务 |
| **库存与供应链服务** | 管理商品/耗材的出入库、盘点、调拨、智能预警。 | 库存与供应链 |
| **数据与报表服务** | 负责从业务库同步数据到数据仓库，提供数据驾驶舱和多维度报表的查询接口。 | 数据决策中心 |
| **AI与算法服务** | 独立的Python服务。提供动态定价建议、智能排班推荐、客户流失预测等算法模型接口。 | AI相关功能 |
| **通知与消息服务** | 统一的通知出口，负责发送短信、微信模板消息、App Push等。 | 各模块的通知需求 |

---

## 4. 部署架构

系统将部署在云服务器上，采用Kubernetes进行容器编排，实现高可用和弹性伸缩。

- **环境规划**:
    - **开发环境(Dev)**: 开发者本地环境或云上共享开发环境。
    - **测试环境(Test/QA)**: 用于功能、集成和性能测试。
    - **预发布环境(Staging)**: 与生产环境配置一致，用于上线前的最终验证。
    - **生产环境(Prod)**: 正式对外提供服务的环境。

- **生产环境部署拓扑 (简化)**:
    - **可用区 (AZ)**: 至少部署在两个可用区，实现跨AZ容灾。
    - **负载均衡 (SLB/ELB)**: 作为流量入口，将请求分发至API网关。
    - **API网关集群**: 部署多个实例，处理认证、路由、限流。
    - **K8s集群**: 所有微服务以Pod形式运行在K8s集群中，配置HPA（Horizontal Pod Autoscaler）实现自动扩缩容。
    - **数据库集群**: MySQL采用主从复制+读写分离架构。Redis采用哨兵或Cluster模式。
    - **CI/CD流水线**: 代码提交后，通过Jenkins/GitLab CI自动触发构建、测试和部署流程。

---

## 5. 关键技术实现思路

### 5.1 数据隔离方案
- **初期方案**: 在单数据库中，所有核心业务表增加 `tenant_id` (租户ID) 和 `store_id` (门店ID) 字段，通过应用层逻辑和数据库中间件（如ShardingSphere）实现数据隔离。
- **未来SaaS化演进**: 随着业务发展，可平滑过渡到按租户分库（Database per Tenant）的模式，实现更彻底的物理隔离。

### 5.2 高并发预约处理
- **流量削峰**: 在预约高峰期，将用户的预约请求先写入消息队列（RabbitMQ），由后端服务异步消费处理，避免数据库瞬时压力过大。
- **库存预扣**: 使用Redis的原子操作（如 `DECR`）来预扣减技师或资源的时间片库存，防止超卖。
- **分布式锁**: 在处理关键资源（如特定技师的特定时间）时，使用Redis分布式锁（如Redisson）确保同一时间只有一个请求能成功预约。

### 5.3 数据同步与报表
- **同步机制**: 采用Canal等工具监听业务主库（MySQL）的binlog，将数据变更实时同步到消息队列，再由消费者写入数据仓库（ClickHouse）。
- **报表查询**: 所有复杂的、跨多个业务领域的报表查询，均在ClickHouse中进行，避免对主业务库造成性能影响。利用ClickHouse的列式存储和并行计算能力，实现报表的秒级响应。

### 5.4 离线操作支持
- **前端数据库**: 在技师端/收银端的Web App中，使用IndexedDB等浏览器本地数据库技术。
- **实现流程**:
    1.  首次加载时，将当日的核心数据（如预约列表、项目价格）缓存到本地。
    2.  在离线状态下，所有操作（如开单）记录在本地数据库。
    3.  通过Service Worker监听网络状态，一旦网络恢复，自动将本地数据同步到服务端，并处理可能的数据冲突.

### 5.5 数据迁移策略
对于计划采用本系统的连锁门店，数据迁移是项目初期至关重要的环节。制定周密的数据迁移策略，是确保业务平滑过渡、降低上线风险的关键。

- **迁移范围**:
    - **核心数据**: 必须优先迁移，包括会员档案（含储值余额、积分）、员工档案、服务与商品库。
    - **历史数据**: 消费记录、历史预约等数据为可选迁移，可根据客户需求和数据量大小决定。
    - **配置数据**: 门店信息、提成方案等基础配置数据。

- **迁移流程**:
    1. **数据评估与清洗**:
        - **数据源分析**: 分析客户现有数据来源（如旧软件数据库、Excel表格、纸质记录）。
        - **数据质量评估**: 检查数据的完整性、一致性和准确性。
        - **数据清洗**: 与客户共同制定数据清洗规则，处理重复、错误或不规范的数据。
    2. **工具开发与适配**:
        - **提供标准模板**: 为Excel导入提供标准化的数据模板，并附有详细的填写说明。
        - **开发通用导入工具**: 开发支持CSV/Excel等通用格式的数据导入工具，支持字段映射和数据校验。
        - **适配主流系统**: 针对市场上几款主流的旧美业管理软件，开发专门的数据抽取脚本。
    3. **迁移执行与验证**:
        - **分阶段执行**: 建议采用“先基础数据，后业务数据”的顺序执行。
        - **试迁移 (模拟)**: 在测试环境中进行至少一次完整的数据迁移演练，并由客户进行数据验证。
        - **正式迁移**: 在正式上线前的非营业时间（如深夜）进行。
        - **数据校验**: 迁移完成后，通过数据总量比对、关键字段抽样核对等方式，确保数据迁移的准确无误。

- **风险与预案**:
    - **数据丢失风险**: 在迁移操作前，必须对源数据进行完整备份。
    - **迁移中断风险**: 迁移工具应支持断点续传。
    - **数据不一致风险**: 制定详细的数据核对清单和流程。 