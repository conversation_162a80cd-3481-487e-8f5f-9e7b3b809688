# 按摩推拿连锁管理系统 - API接口规范 (API)

**版本**: 1.1
**日期**: 2024-07-26
**关联文档**: PRD v1.0, SDD v1.0

## 变更历史

| 版本 | 日期 | 修改人 | 变更描述 |
| :--- | :--- | :--- | :--- |
| 1.0 | 2024-07-25 | 后端架构师 | 初始版本 |
| 1.1 | 2024-07-26 | 后端架构师 | 补充会员、库存和数据报表模块API |

---

## 1. 概述

本API接口规范旨在定义"按摩推拿连锁管理系统"中前后端及微服务之间的数据交换格式、协议和标准。所有接口设计遵循 **RESTful** 风格，采用 **HTTPS** 协议传输，数据格式统一为 **JSON**。

### 1.1 基本URL

- **生产环境**: `https://api.yourdomain.com/v1`
- **预发布环境**: `https://api.staging.yourdomain.com/v1`
- **测试环境**: `https://api.test.yourdomain.com/v1`

### 1.2 HTTP方法

| 方法 | 用途 | 示例 |
| :--- | :--- | :--- |
| `GET` | **查询**资源 | `GET /users/{id}` |
| `POST` | **创建**资源 | `POST /users` |
| `PUT` | **完整更新**资源 | `PUT /users/{id}` |
| `PATCH` | **部分更新**资源 | `PATCH /users/{id}` |
| `DELETE` | **删除**资源 | `DELETE /users/{id}` |

### 1.3 统一响应格式

所有API响应都将遵循以下结构，以方便客户端进行统一处理。

- **成功响应 (`code: 200`)**:
```json
{
  "code": 200,
  "message": "Success",
  "data": { ... } // 单个对象 或 列表对象
}
```

- **列表/分页响应**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [ ... ],
    "pagination": {
      "page": 1,
      "size": 10,
      "total_items": 100,
      "total_pages": 10
    }
  }
}
```

- **失败响应 (`code: 非200`)**:
```json
{
  "code": 40001, // 详细业务错误码
  "message": "请求参数错误: 用户名不能为空",
  "data": null
}
```

### 1.4 状态码与业务码

- **HTTP状态码**: 严格遵循HTTP标准。
    - `200 OK`: 请求成功。
    - `201 Created`: 资源创建成功。
    - `204 No Content`: 资源删除成功。
    - `400 Bad Request`: 请求参数错误或业务逻辑校验失败。
    - `401 Unauthorized`: 未授权，需要登录。
    - `403 Forbidden`: 已认证，但无权访问。
    - `404 Not Found`: 请求的资源不存在。
    - `500 Internal Server Error`: 服务器内部错误。

- **业务错误码 (`code`)**: 用于客户端识别具体业务错误，格式为5位数字。
    - `1xxxx`: 通用错误 (如参数错误、请求频繁)
    - `2xxxx`: 用户与权限相关错误
    - `3xxxx`: 预约与排班相关错误
    - `4xxxx`: 订单与支付相关错误
    - `5xxxx`: 会员与营销相关错误
    - `6xxxx`: 库存与供应链相关错误
    - `7xxxx`: 数据与报表相关错误

### 1.5 身份验证与授权

- 系统采用 **JWT (JSON Web Token)** 进行身份验证。
- 用户登录成功后，服务器返回 `access_token` 和 `refresh_token`。
- `access_token` (有效期较短，如2小时) 用于后续所有需要授权的API请求。
- `refresh_token` (有效期较长，如7天) 用于在 `access_token` 过期后静默刷新获取新的token。
- **请求Header**: `Authorization: Bearer <access_token>`

---

## 2. 接口详解

### 2.1 用户认证接口 (`/auth`)

#### `POST /auth/login` - 用户登录
- **描述**: 用户通过手机号和密码登录。
- **请求体**:
```json
{
  "phone": "13800138000",
  "password": "hashed_password_or_plain_text"
}
```
- **成功响应 (200)**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "access_token": "...",
    "refresh_token": "...",
    "expires_in": 7200,
    "user_info": {
        "id": 1,
        "name": "张三",
        "avatar_url": "...",
        "user_type": "employee",
        "store_id": 101
    }
  }
}
```

#### `POST /auth/register` - 用户注册 (顾客端)
- **描述**: 顾客通过手机和验证码注册。
- **请求体**:
```json
{
  "phone": "13912345678",
  "verification_code": "123456",
  "name": "李四"
}
```
- **成功响应 (201)**: 返回登录成功的数据结构。

#### `POST /auth/refresh-token` - 刷新Token
- **描述**: 使用 refresh_token 获取新的 access_token。
- **请求体**:
```json
{
  "refresh_token": "..."
}
```
- **成功响应 (200)**: 返回新的 `access_token` 和 `refresh_token`。

---

### 2.2 预约与排班接口 (`/appointments`, `/schedules`)

#### `GET /appointments` - 查询预约列表
- **描述**: 根据条件查询预约列表，支持员工、顾客、店长等不同视角。
- **Query参数**:
    - `store_id` (店长/总部): 门店ID
    - `employee_id` (员工): 员工ID
    - `customer_id` (顾客): 顾客ID
    - `start_date`, `end_date`: 日期范围
    - `status`: 预约状态
    - `page`, `size`: 分页参数
- **成功响应 (200)**: 返回预约列表分页数据。

#### `POST /appointments` - 创建新预约 (顾客/员工)
- **描述**: 创建一个新的服务预约。
- **请求体**:
```json
{
  "customer_id": 123,
  "employee_id": 456,
  "service_id": 789,
  "store_id": 101,
  "start_time": "2024-08-01T14:00:00Z"
}
```
- **成功响应 (201)**: 返回新创建的预约详情。

#### `GET /schedules/available-slots` - 查询可预约时间段
- **描述**: 查询指定服务、技师或门店在某天的可预约时间段。
- **Query参数**:
    - `date`: 查询日期 (e.g., "2024-08-01")
    - `store_id`: 门店ID
    - `service_id`: 服务项目ID (用于计算时长)
    - `employee_id` (可选): 指定技师ID
- **成功响应 (200)**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "date": "2024-08-01",
    "slots": [
      {"start_time": "10:00", "end_time": "11:30", "available_employees": [...]},
      {"start_time": "14:00", "end_time": "15:30", "available_employees": [...]}
    ]
  }
}
```
---

### 2.3 订单与支付接口 (`/orders`, `/payments`)

#### `POST /orders` - 创建订单 (开单)
- **描述**: 收银员或员工为顾客创建消费订单。
- **请求体**:
```json
{
  "customer_id": 123,
  "store_id": 101,
  "items": [
    {
      "item_id": 789,
      "item_type": "service",
      "employee_id": 456,
      "price": 298.00,
      "quantity": 1
    },
    {
      "item_id": 101,
      "item_type": "product",
      "price": 188.00,
      "quantity": 1
    }
  ],
  "coupon_id": 55,
  "use_points": 1000 
}
```
- **成功响应 (201)**: 返回待支付的订单详情，包括应付金额。

#### `POST /orders/{id}/pay` - 发起支付
- **描述**: 对指定订单发起支付。
- **请求体**:
```json
{
  "payments": [
    {"method": "balance", "amount": 100.00},
    {"method": "wechat_pay", "amount": 286.00}
  ]
}
```
- **成功响应 (200)**: 如果包含线上支付，返回支付所需的参数 (如微信支付二维码URL)。如果支付完成，返回订单的最终状态。

---

### 2.4 健康档案接口 (`/health-records`)

**注意**: 此模块所有接口都需要严格的权限校验，且返回数据中的敏感信息需脱敏。

#### `GET /health-records/customer/{customerId}` - 查看客户健康档案
- **描述**: 技师或有权限的员工查看顾客的健康档案。
- **成功响应 (200)**: 返回结构化的健康档案信息。

#### `PATCH /health-records/customer/{customerId}` - 更新客户健康档案
- **描述**: 技师服务后更新顾客的健康档案，如标记新的痛点。
- **请求体**: 包含要更新的档案部分，如 `body_map`。
- **成功响应 (200)**: 返回更新后的完整档案。

---

### 2.5 会员与营销接口 (`/members`, `/marketing`)

#### `GET /members` - 查询会员列表
- **描述**: 根据条件查询会员列表。
- **Query参数**:
    - `store_id`: 门店ID
    - `keyword`: 搜索关键词（手机号、姓名）
    - `level_id`: 会员等级ID
    - `page`, `size`: 分页参数
- **成功响应 (200)**: 返回会员列表分页数据。

#### `GET /members/{id}` - 获取会员详情
- **描述**: 获取指定会员的详细信息，包括基本信息、消费记录、储值余额等。
- **成功响应 (200)**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": 123,
    "name": "张三",
    "phone": "138****8000",
    "level": {
      "id": 2,
      "name": "黄金会员"
    },
    "balance": 1280.00,
    "points": 2500,
    "join_date": "2023-05-15",
    "last_visit_date": "2024-07-10",
    "total_consumption": 5680.00,
    "visit_count": 12,
    "tags": ["肩颈问题", "喜欢艾灸"],
    "coupons": [
      {
        "id": 101,
        "name": "满500减50券",
        "value": 50.00,
        "expire_date": "2024-08-30"
      }
    ]
  }
}
```

#### `POST /members/{id}/recharge` - 会员充值
- **描述**: 为会员账户充值。
- **请求体**:
```json
{
  "amount": 1000.00,
  "payment_method": "wechat_pay",
  "store_id": 101,
  "operator_id": 201
}
```
- **成功响应 (200)**: 返回充值结果和赠送信息。

#### `POST /marketing/coupons/issue` - 发放优惠券
- **描述**: 批量发放优惠券给指定会员。
- **请求体**:
```json
{
  "coupon_id": 55,
  "member_ids": [123, 456, 789],
  "expire_days": 30,
  "remark": "七夕活动优惠券"
}
```
- **成功响应 (200)**: 返回发放结果。

#### `POST /marketing/referrals` - 创建转介绍记录
- **描述**: 记录会员转介绍新客户。
- **请求体**:
```json
{
  "referrer_id": 123,
  "referee_phone": "13912345678",
  "referee_name": "王五",
  "store_id": 101
}
```
- **成功响应 (201)**: 返回创建的转介绍记录。

---

### 2.6 库存与供应链接口 (`/inventory`)

#### `GET /inventory/products` - 查询产品列表
- **描述**: 查询产品/耗材列表。
- **Query参数**:
    - `type`: 产品类型 (retail, consumable)
    - `keyword`: 搜索关键词
    - `page`, `size`: 分页参数
- **成功响应 (200)**: 返回产品列表分页数据。

#### `GET /inventory/stock` - 查询库存
- **描述**: 查询指定门店的库存状态。
- **Query参数**:
    - `store_id`: 门店ID
    - `product_id` (可选): 产品ID
    - `low_stock_only` (可选): 是否只查询低库存商品
- **成功响应 (200)**: 返回库存列表。

#### `POST /inventory/in-stock` - 入库
- **描述**: 记录产品入库。
- **请求体**:
```json
{
  "store_id": 101,
  "operator_id": 201,
  "items": [
    {
      "product_id": 301,
      "quantity": 50,
      "batch_no": "BT20240720",
      "expiry_date": "2025-07-20",
      "remark": "总部调拨"
    }
  ]
}
```
- **成功响应 (201)**: 返回入库单详情。

#### `POST /inventory/out-stock` - 出库
- **描述**: 记录产品出库。
- **请求体**:
```json
{
  "store_id": 101,
  "operator_id": 201,
  "reason": "销售",
  "items": [
    {
      "product_id": 301,
      "quantity": 5
    }
  ]
}
```
- **成功响应 (201)**: 返回出库单详情。

#### `POST /inventory/transfers` - 创建调拨单
- **描述**: 创建门店间库存调拨单。
- **请求体**:
```json
{
  "from_store_id": 101,
  "to_store_id": 102,
  "operator_id": 201,
  "items": [
    {
      "product_id": 301,
      "quantity": 20,
      "remark": "紧急调拨"
    }
  ]
}
```
- **成功响应 (201)**: 返回创建的调拨单。

---

### 2.7 数据与报表接口 (`/reports`)

#### `GET /reports/dashboard` - 获取数据驾驶舱
- **描述**: 获取数据驾驶舱核心指标。
- **Query参数**:
    - `store_id` (可选): 门店ID，不传则查询全部门店
    - `date_range`: 日期范围 (today, yesterday, last7days, last30days, custom)
    - `start_date`, `end_date` (当date_range=custom时必传): 自定义日期范围
- **成功响应 (200)**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "revenue": {
      "total": 25680.00,
      "compare_to_previous": 0.15
    },
    "customer_visits": {
      "total": 128,
      "compare_to_previous": 0.08
    },
    "new_members": {
      "total": 15,
      "compare_to_previous": -0.05
    },
    "avg_transaction": {
      "value": 200.62,
      "compare_to_previous": 0.03
    },
    "top_services": [
      {"name": "精油推拿", "revenue": 8500.00},
      {"name": "艾灸调理", "revenue": 6200.00}
    ],
    "top_employees": [
      {"name": "张三", "revenue": 5600.00},
      {"name": "李四", "revenue": 4800.00}
    ]
  }
}
```

#### `GET /reports/revenue` - 营收分析报表
- **描述**: 获取营收分析详细报表数据。
- **Query参数**:
    - `store_id` (可选): 门店ID
    - `date_range`: 日期范围
    - `group_by`: 分组方式 (day, week, month)
- **成功响应 (200)**: 返回按时间维度分组的营收数据。

#### `GET /reports/customer-analysis` - 客户分析报表
- **描述**: 获取客户分析报表，包括RFM分析、客户留存等。
- **Query参数**:
    - `store_id` (可选): 门店ID
    - `date_range`: 日期范围
    - `analysis_type`: 分析类型 (rfm, retention, source)
- **成功响应 (200)**: 返回客户分析数据。

#### `GET /reports/employee-performance` - 员工绩效报表
- **描述**: 获取员工绩效分析报表。
- **Query参数**:
    - `store_id` (可选): 门店ID
    - `employee_id` (可选): 员工ID
    - `date_range`: 日期范围
- **成功响应 (200)**: 返回员工绩效数据，包括服务次数、营收、客户评价等。

#### `GET /reports/ai/customer-health` - AI客户健康度分析
- **描述**: 获取AI生成的客户健康度分析，识别可能流失的客户。
- **Query参数**:
    - `store_id`: 门店ID
    - `threshold`: 健康度阈值，低于此值的客户将被识别为高风险
- **成功响应 (200)**: 返回客户健康度分析结果，包括流失风险评分和建议的干预措施。 