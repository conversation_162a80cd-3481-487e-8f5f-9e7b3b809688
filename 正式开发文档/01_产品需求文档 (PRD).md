# 按摩推拿连锁管理系统 - 产品需求文档 (PRD)

**版本**: 1.1
**日期**: 2024-07-26

## 变更历史

| 版本 | 日期 | 修改人 | 变更描述 |
| :--- | :--- | :--- | :--- |
| 1.0 | 2024-07-25 | 产品经理 | 初始版本 |
| 1.1 | 2024-07-26 | 产品经理 | 明确“团购”功能需求；新增“用户体验与界面设计原则”章节。 |

---

## 1. 项目概述

### 1.1 项目背景与愿景

随着消费升级，按摩推拿行业正从传统手工业向现代化、连锁化、品牌化方向快速发展。然而，许多连锁企业仍沿用落后的管理模式，在**多门店协同运营、多股东权益核算**、员工激励、客户关系维护、精细化营销和库存控制等方面面临巨大挑战。

本项目旨在提供一套**深度垂直、智能驱动、体验卓越、合规安全的“专业按摩推拿连锁运营平台”**。它不仅是一套管理软件，更是一套融合了先进管理思想和增长策略的解决方案。通过本系统，企业能够连接总部、股东、门店、员工与顾客，打通信息孤岛，实现精细化运营，最终在激烈的市场竞争中建立可持续的竞争优势。

### 1.2 核心痛点

| 痛点类别 | 具体描述 |
| :--- | :--- |
| **连锁运营管理体系缺失** | 定价与项目不统一、财务与业务脱节、股东权益模糊、库存管理混乱。 |
| **客户增长与留存乏力** | 营销手段单一、客户关系脆弱、缺少社交裂变渠道。 |
| **员工激励与协同效率低下** | 提成方案僵化、排班与预约冲突、内部协作不畅。 |
| **垂直领域专业性不足** | 缺乏专业的健康档案管理、技师与服务不匹配、特殊耗材与资源管理混乱。 |
| **用户体验与流程复杂** | 移动端操作复杂、无法应对临时加项/换人等灵活场景、数据安全与合规风险高。 |

### 1.3 核心价值主张与成功指标

| 核心价值 | 成功指标 |
| :--- | :--- |
| **实现集团"业财一体化"管理** | - 财务月度对账时间减少80%<br>- **股东分红核算时间减少90%，实现自动化** |
| **构建自动化智能营销体系** | - 客户复购率提升20%以上<br>- 通过转介绍带来的新客占比达到10% |
| **打造高效协同与激励平台** | - 员工薪酬核算实现全自动化<br>- 员工人效提升15% |
| **深化行业专业性与服务质量** | - 客户满意度提升15%<br>- 高价值疗程套餐复购率提升25% |

---

## 2. 用户角色与核心场景 (User Persona & Scenarios)

### 2.1 总部管理层/股东
- **角色简介**: 集团决策者、品牌管理者、投资者。
- **核心需求**: 全局管控，掌握集团整体经营数据，制定发展战略，清晰了解投资回报。
- **关键场景**:
    - **数据驾驶舱**: 实时查看集团总营收、利润、客流等核心指标。
    - **股东分红**: 配置复杂的股权结构，系统自动核算分红并生成报表。
    - **多店对比**: 横向对比各门店的业绩、成本、利润率，发现经营问题。

### 2.2 门店店长
- **角色简介**: 门店的日常运营管理者。
- **核心需求**: 高效管理门店日常运营，提升门店业绩。
- **关键场景**:
    - **移动驾驶舱**: 在手机上实时查看今日营业额、预约数、技师空闲状态。
    - **智能排班**: 使用“AI推荐排班”模式，一键生成客户满意度和门店收益理论上最高的排班表。
    - **成本管理**: 精细化录入和核算门店的房租、水电、物料等成本。

### 2.3 员工 (技师/助理)
- **角色简介**: 服务的核心提供者。
- **核心需求**: 清晰了解个人工作安排和业绩提成，提升个人服务效率，操作简便。
- **关键场景**:
    - **极简工作台**: 打开手机App，以时间轴形式清晰看到当天的工作安排。
    - **服务开单**: 服务过程中，为客户临时增加的项目可以方便地在原订单上添加。
    - **业绩查询**: 随时查看个人预估工资、提成明细和服务历史。
    - **健康档案**: 为客户在可视化身体图谱上标记痛点，并创建长期调理方案。

### 2.4 收银员
- **角色简介**: 门店交易的执行者。
- **核心需求**: 快速为会员或散客开单、结算，支持多种支付和核销方式，确保账目准确。
- **关键场景**:
    - **快速开单**: 散客开单，只需输入手机号和称呼即可；会员可快速检索。
    - **混合支付**: 支持顾客使用“会员卡余额 + 微信支付”等组合方式付款。
    - **团购验券**: 快速核销来自美团、抖音等不同平台的团购券。

### 2.5 顾客 (会员)
- **角色简介**: 服务的最终消费者。
- **核心需求**: 方便地在线预约，管理个人账户，查看消费记录，享受专业的个性化服务。
- **关键场景**:
    - **小程序预约**: 在微信小程序上根据“调理目标”智能匹配技师，并完成预约。
    - **账户管理**: 查看自己的储值余额、积分、优惠券，以及每次的消费电子小票。
    - **家庭共享**: 将自己的储值卡与家人共享使用。

---

## 3. 功能性需求 (Functional Requirements)

### 模块一：基础管理 (总部/店长)
| 功能点 | 需求描述 |
| :--- | :--- |
| **门店管理** | 创建、编辑、停用门店信息，包括地址、营业时间、小票LOGO等。 |
| **股东与股权管理** | 管理股东档案、出资记录、股权结构。内置多种分红模型，自动核算分红。支持权益变动追溯。 |
| **员工档案** | 管理员工信息、职位、技能标签（支持手法、领域、等级等多维度）、权限、证照附件。 |
| **服务与商品库** | 总部建立标准项目库，门店可独立定价和启停。项目需关联所需技能标签和耗材BOM清单。 |
| **动态提成方案** | 支持按实收/原价、固定/百分比、阶梯、角色、项目分类等多维度配置提成方案。 |
| **房间/设备管理** | 将特定房间（如艾灸房）或设备（如理疗仪）作为可预约资源进行管理，避免冲突。 |
| **知识库** | 企业内部知识共享中心，用于员工培训和SOP下发。 |

### 模块二：会员运营与营销 (全端)
| 功能点 | 需求描述 |
| :--- | :--- |
| **会员360°视图** | 包含客户基本信息、消费数据、标签，并新增独立的**结构化健康档案**模块（主诉、可视化身体图谱、调理偏好、禁忌）。 |
| **储值与等级体系** | 灵活配置充值赠送规则和会员等级成长体系。支持多人共享一张会员卡。 |
| **营销活动中心** | 提供满减券、折扣券、体验券、拼团、限时折扣等多样化营销工具。 |
| **团购活动管理** | 支持创建和管理对接主流平台（如美团、抖音）的团购套餐，提供独立的验券功能。 |
| **客户转介绍体系** | 为每位会员生成专属推荐码，新客消费后老客自动获得奖励（积分/优惠券/储值金）。 |
| **营销自动化 (MA)** | 1. **AI客户健康度诊断**: 综合RFM、评价、互动数据，预测濒临流失客户。<br>2. **自动化干预**: 触发多步骤、多渠道的挽回流程（文章推送 -> 专属券 -> 人工关怀任务）。 |

### 模块三：核心运营 (全端)
| 功能点 | 需求描述 |
| :--- | :--- |
| **可视化预约日历** | 多维度（技师/房间/设备）日历视图，支持冲突检测和15分钟操作锁定。 |
| **智能排班系统** | 在轮钟、业绩优先等规则外，增加“AI推荐排班”模式，综合技师技能、客户偏好、历史匹配度、技师疲劳度等因素推荐最优排班。 |
| **个性化调理方案** | 技师可为客户创建长期的、有明确健康目标的调理方案，系统自动规划服务间隔并提醒。 |
| **顾客随访** | 系统化安排和提醒员工进行售后关怀，支持配置随访模板。 |
| **多平台评价管理** | 通过API或RPA技术，自动同步美团、点评等平台的客户评价。 |

### 模块四：收银与财务 (收银/员工/店长/总部)
| 功能点 | 需求描述 |
| :--- | :--- |
| **收银台 (POS)** | 支持会员/散客开单，混合支付，多渠道核销。支持服务中加项/换人。支持“手工记账”或“内部挂账”。 |
| **AI动态定价** | AI基于时段、技师闲置率、天气、节假日等多维度因素，推荐**最优价格浮动区间**和**智能套餐组合**。 |
| **电子小票** | 支付成功后自动通过微信推送电子小票。 |
| **精细化成本管理** | 精细化记录和核算门店房租、水电、人力、耗材等成本。服务完成后，根据BOM清单自动扣减耗材库存。 |
| **财务软件集成** | 自动生成凭证，对接到主流财务软件（用友、金蝶）。 |

### 模块五：库存与供应链 (店长/总部)
| 功能点 | 需求描述 |
| :--- | :--- |
| **库存管理** | 管理零售商品和内部耗材的出入库、盘点，支持批次效期管理和追溯。 |
| **智能预警** | 自动监控低库存和近效期商品，并发送通知。 |
| **门店间调拨** | 支持门店间的库存调拨申请、审批、出库、入库全流程跟踪。 |

### 模块六：员工端 (H5/小程序)
| 功能点 | 需求描述 |
| :--- | :--- |
| **极简工作台** | “大字号、流程化”的极简模式，首页以时间轴展示当日工作日程。 |
| **个人业绩** | 实时查询个人服务历史和预估工资（含提成明细）。客户信息默认脱敏显示。 |
| **假勤与调班** | 在线提交请假、调班申请，店长审批后自动更新排班和预约系统。 |
| **客户健康档案查阅** | 可查看被服务客户的健康档案，并在服务后进行更新。 |

### 模块七：数据决策中心 (店长/股东/总部)
| 功能点 | 需求描述 |
| :--- | :--- |
| **数据驾驶舱** | 图表化展示核心经营指标（营收、客流、利润、人效等）。店长端App首页应为可自定义的移动驾驶舱。 |
| **多维度分析报表** | 提供营业分析、员工业绩、会员RFM分析、成本利润、评价分析等多种可钻取报表。 |
| **AI绩效看板 (员工)** | 为技师生成个人绩效雷达图，与店内平均水平进行横向对比，并提出具体提升建议。 |

---

## 4. 非功能性需求 (Non-Functional Requirements)

### 4.1 性能需求
- **并发能力**: 系统需支持至少1000个并发用户访问。
- **响应时间**: 核心接口（如开单、预约）响应时间低于500ms，报表查询在秒级响应。
- **高可用性**: 采用高可用架构，实施数据备份与灾备预案，确保99.9%的业务连续性。
- **离线能力**: 技师端和收银端应支持核心功能（查看预约、开单）的离线操作，网络恢复后自动同步。

### 4.2 安全与合规需求
- **数据加密**: 用户手机号、密码、健康档案等敏感信息必须加密存储。
- **权限控制 (RBAC)**: 具备严格的角色权限控制，支持按门店、数据类型进行精细化数据隔离，防止数据越权访问。
- **健康信息合规**:
    - **单独授权**: 收集客户健康信息时，必须有独立的弹窗，需客户单独勾选同意。
    - **数据脱敏**: 在非必要场景（如报表、员工业绩查询），客户敏感信息默认脱敏显示。
    - **数据删除权**: 客户在小程序端应有清晰路径申请删除其个人数据。
- **操作安全**: 对大额充值、批量导出等敏感操作增加二次验证机制（如短信验证码）。

### 4.3 财务严谨性
- **每日对账**: 自动比对系统线上支付金额与第三方支付平台的实际到账金额。
- **退款流程**: 设计严格的退款审批流程，明确不同角色的退款权限和额度。

### 4.4 扩展性需求
- **插件化架构**: 核心功能模块化，便于未来功能扩展。
- **多租户支持**: 在数据库设计层面预留租户ID字段，为未来SaaS化做准备。
- **开放API**: 提供标准的Open API，便于与企业微信、钉钉、小程序商城等第三方系统集成.

### 4.5 用户体验与界面设计原则 (UX/UI Principles)
- **角色化设计**: 针对不同用户角色（如店长、技师、收银员、顾客）提供定制化的界面和流程，隐藏无关功能，突出核心操作。
- **极简主义 (员工端)**: 技师端和收银端必须遵循“大字号、流程化、防误触”的设计原则。界面元素清晰，核心操作（如开单、查看预约）路径不超过3次点击。
- **数据可视化**: 总部、股东和店长端应广泛使用图表，将复杂的经营数据以直观、易懂的方式呈现。
- **移动优先**: 除总部后台和收银端外，所有功能设计均需优先考虑移动端（小程序/H5）的展示效果和操作体验。
- **反馈及时**: 用户的任何操作都应有即时的系统反馈（如加载提示、成功toast、错误警告），避免用户困惑和焦虑。 